<UserControl x:Class="Planna.EventsBudgetEditor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Planna"
             mc:Ignorable="d" 
             d:DesignHeight="32" d:DesignWidth="140">
    <UserControl.Resources>
        <!-- Modern input style -->
        <Style x:Key="BudgetTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E1DFDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text="$" 
                                          Foreground="#605E5C" 
                                          FontWeight="SemiBold"
                                          VerticalAlignment="Center"
                                          Margin="8,0,2,0"/>
                                
                                <ScrollViewer Grid.Column="1" x:Name="PART_ContentHost" 
                                            Margin="{TemplateBinding Padding}"
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#0078D4"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="#C8C6C4"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="QuickButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F8F8F8"/>
            <Setter Property="BorderBrush" Value="#E1DFDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="#323130"/>
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Padding" Value="4,2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E1DFDD"/>
                                <Setter Property="BorderBrush" Value="#C8C6C4"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#D2D0CE"/>
                                <Setter Property="BorderBrush" Value="#B3B0AD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <TextBox Grid.Row="0" x:Name="BudgetTextBox"
                 Style="{StaticResource BudgetTextBoxStyle}"
                 ToolTip="Enter monthly events budget"/>
        
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,2,0,0">
            <Button Content="1K" Style="{StaticResource QuickButtonStyle}" Margin="0,0,2,0" Click="QuickAmount_Click" Tag="1000"/>
            <Button Content="2.5K" Style="{StaticResource QuickButtonStyle}" Margin="0,0,2,0" Click="QuickAmount_Click" Tag="2500"/>
            <Button Content="5K" Style="{StaticResource QuickButtonStyle}" Margin="0,0,2,0" Click="QuickAmount_Click" Tag="5000"/>
            <Button Content="10K" Style="{StaticResource QuickButtonStyle}" Click="QuickAmount_Click" Tag="10000"/>
        </StackPanel>
    </Grid>
</UserControl>
