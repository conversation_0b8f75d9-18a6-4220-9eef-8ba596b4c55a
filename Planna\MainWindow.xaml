<Window x:Class="Planna.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Planna"
        xmlns:oxy="http://oxyplot.org/wpf"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="Planna - Growth Planning Tool"
        Height="1000" Width="1600"
        MinHeight="800" MinWidth="1200"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">
    
    <Grid>
        <Grid.RowDefinitions>
            <!-- Header -->
            <RowDefinition Height="Auto"/>
            <!-- Business Assumptions Panel (Always Visible) -->
            <RowDefinition Height="Auto"/>
            <!-- Main Content: Chart + Unit Economics -->
            <RowDefinition Height="3*"/>
            <!-- Monthly Planning Table -->
            <RowDefinition Height="2*"/>
            <!-- Status Bar -->
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,8">
            <Border Background="{StaticResource AccentBrush}" Padding="24,16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="TrendingUp" Width="32" Height="32" Foreground="White" Margin="0,0,16,0" VerticalAlignment="Center"/>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="Planna" FontSize="28" FontWeight="Bold" Foreground="White"/>
                            <TextBlock Text="Growth Planning Tool" FontSize="14" Foreground="White" Opacity="0.9"/>
                        </StackPanel>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}" Content="New" Click="NewScenario_Click"
                                Margin="0,0,8,0" Foreground="White" BorderBrush="White" Padding="16,8" ToolTip="Create new scenario"/>
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}" Content="Load" Click="LoadScenario_Click"
                                Margin="0,0,8,0" Foreground="White" BorderBrush="White" Padding="16,8" ToolTip="Load scenario from file"/>
                        <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="Save" Click="SaveScenario_Click"
                                Margin="0,0,8,0" Background="White" Foreground="{StaticResource AccentBrush}" Padding="16,8" ToolTip="Save current scenario"/>
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}" Content="Save As..." Click="SaveAsScenario_Click"
                                Margin="0,0,16,0" Foreground="White" BorderBrush="White" Padding="16,8" ToolTip="Save scenario with new name"/>
                        <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="Help"
                                Margin="0,0,8,0" Background="White" Foreground="{StaticResource AccentBrush}" Padding="16,8"/>
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}" Content="Export"
                                Margin="0,0,0,0" Foreground="White" BorderBrush="White" Padding="16,8"/>
                    </StackPanel>
                </Grid>
            </Border>
        </materialDesign:Card>

        <!-- Business Assumptions Panel (Always Visible) -->
        <materialDesign:Card Grid.Row="1" materialDesign:ElevationAssist.Elevation="Dp1" Margin="8,0,8,8">
            <Border Background="{StaticResource SurfaceBrush}" Padding="24,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Section Header -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                        <materialDesign:PackIcon Kind="Settings" Width="24" Height="24" Foreground="{StaticResource AccentBrush}" Margin="0,0,8,0" VerticalAlignment="Center"/>
                        <TextBlock Text="Business Assumptions" FontSize="20" FontWeight="SemiBold" Foreground="{StaticResource TextPrimaryBrush}" VerticalAlignment="Center"/>
                        <TextBlock Text="Configure global settings that apply across all months" FontSize="14" Foreground="{StaticResource TextSecondaryBrush}" Margin="16,0,0,0" VerticalAlignment="Center"/>
                    </StackPanel>

                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                
                <!-- Revenue Settings -->
                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                    <TextBlock Text="Revenue Settings" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>
                    <TextBlock Text="Pricing Tiers" FontWeight="Medium" Margin="0,0,0,5"/>
                    
                    <StackPanel>
                        <TextBlock Text="Basic Plan ($)" FontSize="11" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding BasicPlan, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                        <TextBlock Text="Standard Plan ($)" FontSize="11" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding StandardPlan, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                        <TextBlock Text="Professional Plan ($)" FontSize="11" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding ProfessionalPlan, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                        <TextBlock Text="Enterprise Plan ($)" FontSize="11" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding EnterprisePlan, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    </StackPanel>

                    <Border Background="{StaticResource AccentBrush}" CornerRadius="5" Padding="10" Margin="0,10">
                        <TextBlock Foreground="White" FontWeight="SemiBold" HorizontalAlignment="Center">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="Blended ARPU: ${0:F0}">
                                    <Binding Path="BlendedARPU"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                    </Border>
                </StackPanel>
                
                <!-- Customer Mix -->
                <StackPanel Grid.Column="1" Margin="0,0,15,0">
                    <TextBlock Text="Customer Mix" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                    <StackPanel>
                        <!-- Basic Plan Mix -->
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Basic" FontSize="11" VerticalAlignment="Center" Width="60"/>
                            <Slider Grid.Column="1" Value="{Binding BasicMixPercent, UpdateSourceTrigger=PropertyChanged}" Minimum="0" Maximum="100"
                                    TickFrequency="5" IsSnapToTickEnabled="True" Margin="5,0"/>
                            <TextBox Grid.Column="2" Text="{Binding BasicMixPercent, UpdateSourceTrigger=PropertyChanged}"
                                     Width="40" FontSize="11" HorizontalContentAlignment="Center"/>
                        </Grid>

                        <!-- Standard Plan Mix -->
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Standard" FontSize="11" VerticalAlignment="Center" Width="60"/>
                            <Slider Grid.Column="1" Value="{Binding StandardMixPercent, UpdateSourceTrigger=PropertyChanged}" Minimum="0" Maximum="100"
                                    TickFrequency="5" IsSnapToTickEnabled="True" Margin="5,0"/>
                            <TextBox Grid.Column="2" Text="{Binding StandardMixPercent, UpdateSourceTrigger=PropertyChanged}"
                                     Width="40" FontSize="11" HorizontalContentAlignment="Center"/>
                        </Grid>

                        <!-- Professional Plan Mix -->
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Pro" FontSize="11" VerticalAlignment="Center" Width="60"/>
                            <Slider Grid.Column="1" Value="{Binding ProfessionalMixPercent, UpdateSourceTrigger=PropertyChanged}" Minimum="0" Maximum="100"
                                    TickFrequency="1" IsSnapToTickEnabled="True" Margin="5,0"/>
                            <TextBox Grid.Column="2" Text="{Binding ProfessionalMixPercent, UpdateSourceTrigger=PropertyChanged}"
                                     Width="40" FontSize="11" HorizontalContentAlignment="Center"/>
                        </Grid>

                        <!-- Enterprise Plan Mix -->
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Enterprise" FontSize="11" VerticalAlignment="Center" Width="60"/>
                            <Slider Grid.Column="1" Value="{Binding EnterpriseMixPercent, UpdateSourceTrigger=PropertyChanged}" Minimum="0" Maximum="100"
                                    TickFrequency="1" IsSnapToTickEnabled="True" Margin="5,0"/>
                            <TextBox Grid.Column="2" Text="{Binding EnterpriseMixPercent, UpdateSourceTrigger=PropertyChanged}"
                                     Width="40" FontSize="11" HorizontalContentAlignment="Center"/>
                        </Grid>

                        <!-- Validation Message -->
                        <Border CornerRadius="5" Padding="8" Margin="0,10">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Setter Property="Background" Value="LightGreen"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsCustomerMixValid}" Value="False">
                                            <Setter Property="Background" Value="LightCoral"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <TextBlock Text="{Binding CustomerMixValidationMessage}" FontSize="11" FontWeight="SemiBold"
                                       HorizontalAlignment="Center" Foreground="White"/>
                        </Border>
                    </StackPanel>
                </StackPanel>
                
                <!-- Team Productivity -->
                <StackPanel Grid.Column="2" Margin="0,0,15,0">
                    <TextBlock Text="Team Productivity" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                    <TextBlock Text="Sales Team" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBlock Text="Customers per Rep" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding CustomersPerSalesRep, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Ramp Time (months)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding SalesRampMonths, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                    <TextBlock Text="Marketing Team" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBlock Text="Leads per Marketer" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding LeadsPerMarketer, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Lead Conversion %" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding LeadConversionPercent, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                    <TextBlock Text="Financial Settings" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBlock Text="Free-to-Pay Conversion %" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding FreeToPayConversionPercent, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Monthly Churn %" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding MonthlyChurnPercent, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Initial Cash ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding InitialCash, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                </StackPanel>
                
                <!-- Salaries & Costs -->
                <StackPanel Grid.Column="3">
                    <TextBlock Text="Monthly Salaries" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                    <TextBlock Text="Sales Rep ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding SalesRepSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Marketing ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding MarketingSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Developer ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding DeveloperSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Operations ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding OperationsSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Leadership ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding LeadershipSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                    <TextBlock Text="Fixed Monthly Costs ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding FixedMonthlyCosts, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                    <TextBlock Text="Event Lead Generation" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBlock Text="Base Rate (leads/$1000)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding EventLeadGenerationRate, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Medium Event Threshold ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding MediumEventThreshold, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Medium Event Scaling (%)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding MediumEventScalingFactor, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Large Event Threshold ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding LargeEventThreshold, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Large Event Scaling (%)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding LargeEventScalingFactor, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                </StackPanel>
            </Grid>
        </Expander>

        <!-- Main Content Area: Chart + Unit Economics -->
        <Grid Grid.Row="2" Margin="20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Chart Area -->
            <Border Grid.Column="0" Background="White" BorderBrush="#E1DFDD" BorderThickness="1" Margin="0,0,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Chart Header -->
                    <Border Grid.Row="0" Background="{StaticResource AccentBrush}" Padding="15,10">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="24" Height="24" Fill="White" Margin="0,0,10,0"/>
                            <StackPanel>
                                <TextBlock Text="Growth Visualization" FontWeight="SemiBold" Foreground="White"/>
                                <TextBlock Text="Track key metrics over time" FontSize="11" Foreground="White" Opacity="0.8"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                    
                    <!-- Metric Selection -->
                    <WrapPanel Grid.Row="1" Margin="15,10" Orientation="Horizontal">
                        <ToggleButton x:Name="MRRToggle" Content="MRR" IsChecked="True" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                        <ToggleButton x:Name="CustomersToggle" Content="Customers" IsChecked="True" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                        <ToggleButton x:Name="CashToggle" Content="Cash on Hand" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                        <ToggleButton x:Name="BurnToggle" Content="Monthly Burn" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                        <ToggleButton x:Name="TeamToggle" Content="Team Size" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                        <ToggleButton x:Name="RunwayToggle" Content="Runway" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                        <ToggleButton x:Name="FreeUsersEventsToggle" Content="Free Users (Events)" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                        <ToggleButton x:Name="CustomersEventsToggle" Content="Customers (Events)" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                    </WrapPanel>
                    
                    <!-- Chart -->
                    <oxy:PlotView Grid.Row="2" Margin="15" x:Name="GrowthChart"/>
                </Grid>
            </Border>
            
            <!-- Unit Economics Panel -->
            <Border Grid.Column="1" Background="White" BorderBrush="#E1DFDD" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Unit Economics Header -->
                    <Border Grid.Row="0" Background="{StaticResource AccentBrush}" Padding="15,10">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="24" Height="24" Fill="White" Margin="0,0,10,0"/>
                            <StackPanel>
                                <TextBlock Text="Unit Economics" FontWeight="SemiBold" Foreground="White"/>
                                <TextBlock Text="{Binding SelectedMonthProjection.Month, RelativeSource={RelativeSource AncestorType=Window}}" FontSize="11" Foreground="White" Opacity="0.8"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                    
                    <!-- Investor-Grade Metrics -->
                    <ScrollViewer Grid.Row="1" Padding="15">
                        <StackPanel DataContext="{Binding SelectedMonthProjection, RelativeSource={RelativeSource AncestorType=Window}}">

                            <!-- Key Revenue Metrics -->
                            <Border Background="#F8F9FA" BorderBrush="#E1DFDD" BorderThickness="1" CornerRadius="5" Padding="15" Margin="0,0,0,10">
                                <StackPanel>
                                    <TextBlock Text="📊 Revenue Metrics" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                                    <Grid Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="{StaticResource AccentBrush}"
                                                CornerRadius="5" Padding="8" Margin="0,0,3,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="Total Customers" FontSize="9" Foreground="White" HorizontalAlignment="Center"/>
                                                <TextBlock Text="{Binding TotalCustomers, StringFormat=N0}" FontSize="16" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>

                                        <Border Grid.Column="1" Background="Green"
                                                CornerRadius="5" Padding="8" Margin="3,0,0,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="New This Month" FontSize="9" Foreground="White" HorizontalAlignment="Center"/>
                                                <TextBlock FontSize="16" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center">
                                                    <TextBlock.Text>
                                                        <MultiBinding StringFormat="+{0:N0}">
                                                            <Binding Path="NewCustomers"/>
                                                        </MultiBinding>
                                                    </TextBlock.Text>
                                                </TextBlock>
                                            </StackPanel>
                                        </Border>
                                    </Grid>

                                    <StackPanel>
                                        <Grid Margin="0,0,0,3">
                                            <TextBlock Text="MRR" FontSize="11"/>
                                            <TextBlock Text="{Binding MRR, StringFormat=C0}" FontSize="11" FontWeight="Bold" Foreground="{StaticResource AccentBrush}" HorizontalAlignment="Right"/>
                                        </Grid>
                                        <Grid Margin="0,0,0,3">
                                            <TextBlock Text="ARR" FontSize="11"/>
                                            <TextBlock Text="{Binding ARR, StringFormat=C0}" FontSize="11" FontWeight="Bold" Foreground="Purple" HorizontalAlignment="Right"/>
                                        </Grid>
                                        <Grid Margin="0,0,0,3">
                                            <TextBlock Text="Growth Rate" FontSize="11"/>
                                            <TextBlock Text="{Binding MonthlyGrowthRate, StringFormat='{}{0:F1}%'}" FontSize="11" FontWeight="Bold" HorizontalAlignment="Right">
                                                <TextBlock.Foreground>
                                                    <SolidColorBrush Color="{Binding GrowthHealth, Converter={StaticResource HealthToColorConverter}}"/>
                                                </TextBlock.Foreground>
                                            </TextBlock>
                                        </Grid>
                                        <Grid Margin="0,0,0,3">
                                            <TextBlock Text="Free Users from Events" FontSize="11"/>
                                            <TextBlock Text="{Binding FreeUsersFromEvents, StringFormat=N0}" FontSize="11" FontWeight="Bold" Foreground="DarkOrange" HorizontalAlignment="Right"/>
                                        </Grid>
                                        <Grid>
                                            <TextBlock Text="Customers from Events" FontSize="11"/>
                                            <TextBlock Text="{Binding NewCustomersFromEvents, StringFormat=N0}" FontSize="11" FontWeight="Bold" Foreground="DarkGreen" HorizontalAlignment="Right"/>
                                        </Grid>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- LTV:CAC Ratio - Key Investor Metric -->
                            <Border BorderBrush="#E1DFDD" BorderThickness="1" CornerRadius="5" Padding="15" Margin="0,0,0,10">
                                <Border.Background>
                                    <SolidColorBrush Color="{Binding LTVCACHealth, Converter={StaticResource HealthToColorConverter}}" Opacity="0.1"/>
                                </Border.Background>
                                <StackPanel>
                                    <Grid Margin="0,0,0,10">
                                        <TextBlock Text="🎯 LTV:CAC Ratio" FontWeight="SemiBold" FontSize="14"/>
                                        <TextBlock Text="{Binding LTVCACHealth, Converter={StaticResource HealthToIconConverter}}" FontSize="16" HorizontalAlignment="Right"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="{Binding LTVToCACRatio, StringFormat='{}{0:F1}:1'}" FontSize="24" FontWeight="Bold">
                                                <TextBlock.Foreground>
                                                    <SolidColorBrush Color="{Binding LTVCACHealth, Converter={StaticResource HealthToColorConverter}}"/>
                                                </TextBlock.Foreground>
                                            </TextBlock>
                                            <TextBlock Text="{Binding LTVCACHealth, Converter={StaticResource HealthToMessageConverter}, ConverterParameter='LTV:CAC'}"
                                                       FontSize="10" TextWrapping="Wrap" Margin="0,5"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                                            <TextBlock Text="Benchmarks:" FontSize="10" FontWeight="SemiBold"/>
                                            <TextBlock Text="🟢 Excellent: 5:1+" FontSize="9"/>
                                            <TextBlock Text="🟡 Good: 3:1+" FontSize="9"/>
                                            <TextBlock Text="🟠 OK: 2:1+" FontSize="9"/>
                                            <TextBlock Text="🔴 Poor: &lt;2:1" FontSize="9"/>
                                        </StackPanel>
                                    </Grid>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="LTV" FontSize="10" Foreground="Gray"/>
                                            <TextBlock Text="{Binding LTV, StringFormat=C0}" FontSize="12" FontWeight="SemiBold"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="CAC" FontSize="10" Foreground="Gray"/>
                                            <TextBlock Text="{Binding CAC, StringFormat=C0}" FontSize="12" FontWeight="SemiBold"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- Payback Period -->
                            <Border BorderBrush="#E1DFDD" BorderThickness="1" CornerRadius="5" Padding="15" Margin="0,0,0,10">
                                <Border.Background>
                                    <SolidColorBrush Color="{Binding PaybackHealth, Converter={StaticResource HealthToColorConverter}}" Opacity="0.1"/>
                                </Border.Background>
                                <StackPanel>
                                    <Grid Margin="0,0,0,5">
                                        <TextBlock Text="⏱️ Payback Period" FontWeight="SemiBold" FontSize="14"/>
                                        <TextBlock Text="{Binding PaybackHealth, Converter={StaticResource HealthToIconConverter}}" FontSize="16" HorizontalAlignment="Right"/>
                                    </Grid>

                                    <TextBlock Text="{Binding PaybackPeriodMonths, StringFormat='{}{0:F1} months'}" FontSize="20" FontWeight="Bold">
                                        <TextBlock.Foreground>
                                            <SolidColorBrush Color="{Binding PaybackHealth, Converter={StaticResource HealthToColorConverter}}"/>
                                        </TextBlock.Foreground>
                                    </TextBlock>
                                    <TextBlock Text="{Binding PaybackHealth, Converter={StaticResource HealthToMessageConverter}, ConverterParameter='Payback'}"
                                               FontSize="10" TextWrapping="Wrap" Margin="0,5"/>
                                </StackPanel>
                            </Border>

                            <!-- Financial Health -->
                            <Border BorderBrush="#E1DFDD" BorderThickness="1" CornerRadius="5" Padding="15">
                                <Border.Background>
                                    <SolidColorBrush Color="{Binding RunwayHealth, Converter={StaticResource HealthToColorConverter}}" Opacity="0.1"/>
                                </Border.Background>
                                <StackPanel>
                                    <TextBlock Text="💰 Financial Health" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                                    <Grid Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="{StaticResource AccentBrush}"
                                                CornerRadius="5" Padding="8" Margin="0,0,3,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="Cash on Hand" FontSize="9" Foreground="White" HorizontalAlignment="Center"/>
                                                <TextBlock Text="{Binding CashOnHand, StringFormat=C0}" FontSize="14" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>

                                        <Border Grid.Column="1" Background="Red"
                                                CornerRadius="5" Padding="8" Margin="3,0,0,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="Monthly Burn" FontSize="9" Foreground="White" HorizontalAlignment="Center"/>
                                                <TextBlock Text="{Binding MonthlyBurn, StringFormat=C0}" FontSize="14" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>

                                    <Border CornerRadius="5" Padding="10" Margin="0,5">
                                        <Border.Background>
                                            <SolidColorBrush Color="{Binding RunwayHealth, Converter={StaticResource HealthToColorConverter}}"/>
                                        </Border.Background>
                                        <StackPanel>
                                            <Grid>
                                                <TextBlock Text="Runway" FontSize="12" Foreground="White"/>
                                                <TextBlock Text="{Binding RunwayHealth, Converter={StaticResource HealthToIconConverter}}" FontSize="14" Foreground="White" HorizontalAlignment="Right"/>
                                            </Grid>
                                            <TextBlock Text="{Binding RunwayMonths, StringFormat='{}{0:F1} months'}" FontSize="16" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            <ProgressBar Value="{Binding RunwayMonths}" Maximum="24" Height="6" Margin="0,5"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Border>

                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- Monthly Planning Table -->
        <Border Grid.Row="3" Background="White" BorderBrush="#E1DFDD" BorderThickness="1" Margin="20,10,20,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- Table Header -->
                <Border Grid.Row="0" Background="{StaticResource AccentBrush}" Padding="15,10">
                    <Grid>
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="24" Height="24" Fill="White" Margin="0,0,10,0"/>
                            <StackPanel>
                                <TextBlock Text="Monthly Team Planning" FontWeight="SemiBold" Foreground="White"/>
                                <TextBlock Text="Plan team growth and expenses month by month" FontSize="11" Foreground="White" Opacity="0.8"/>
                            </StackPanel>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Border Background="White" CornerRadius="10" Padding="8,2" Margin="0,0,10,0" Opacity="0.9">
                                <TextBlock x:Name="MonthCountTextBlock" Text="10 months planned" FontSize="10" Foreground="{StaticResource AccentBrush}"/>
                            </Border>
                            <Button Content="📋 Copy Previous" Background="White" Foreground="{StaticResource AccentBrush}" Padding="8,5" Margin="0,0,5,0" FontWeight="SemiBold"
                                    ToolTip="Copy values from previous month to selected months"
                                    Click="CopyPreviousMonth_Click"/>
                            <Button Content="📊 Templates" Background="White" Foreground="{StaticResource AccentBrush}" Padding="8,5" Margin="0,0,5,0" FontWeight="SemiBold"
                                    ToolTip="Apply growth templates (Conservative, Aggressive, etc.)"
                                    Click="ApplyTemplate_Click"/>
                            <Button Content="➖ Remove Last" Background="White" Foreground="#F7630C" Padding="8,5" Margin="0,0,5,0" FontWeight="SemiBold"
                                    ToolTip="Remove the last month from planning"
                                    Click="RemoveMonth_Click"/>
                            <Button Content="🧪 Test Validation" Background="White" Foreground="#F7630C" Padding="8,5" Margin="0,0,5,0" FontWeight="SemiBold"
                                    ToolTip="Test validation system with extreme values"
                                    Click="TestValidation_Click"/>
                            <Button Content="➕ Add Month" Background="White" Foreground="{StaticResource AccentBrush}" Padding="10,5" FontWeight="SemiBold"
                                    Click="AddMonth_Click"/>
                        </StackPanel>
                    </Grid>
                </Border>
                
                <!-- Data Grid -->
                <ScrollViewer Grid.Row="1" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" Margin="15">
                    <DataGrid x:Name="MonthlyPlanningGrid"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              CanUserSortColumns="False"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              Background="White"
                              AlternatingRowBackground="#FAFAFA"
                              RowHeight="36"
                              FontSize="12"
                              HorizontalScrollBarVisibility="Disabled"
                              VerticalScrollBarVisibility="Disabled">
                    <DataGrid.Resources>
                        <!-- Header Style -->
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#F8F8F8"/>
                            <Setter Property="Foreground" Value="#323130"/>
                            <Setter Property="FontWeight" Value="SemiBold"/>
                            <Setter Property="FontSize" Value="11"/>
                            <Setter Property="Padding" Value="8,6"/>
                            <Setter Property="BorderBrush" Value="#E1DFDD"/>
                            <Setter Property="BorderThickness" Value="0,0,1,1"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        </Style>

                        <!-- Row Style -->
                        <Style TargetType="DataGridRow">
                            <Setter Property="Background" Value="White"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#F3F2F1"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#E3F2FD"/>
                                    <Setter Property="BorderBrush" Value="#0078D4"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>

                        <!-- Cell Style -->
                        <Style TargetType="DataGridCell">
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Padding" Value="8,4"/>
                            <Setter Property="VerticalAlignment" Value="Center"/>
                            <Style.Triggers>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Setter Property="Foreground" Value="#323130"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Month" Binding="{Binding Month}" IsReadOnly="True" Width="100"/>

                        <!-- Validation Feedback Column - Moved to be more visible -->
                        <DataGridTextColumn Header="Feedback" Binding="{Binding ValidationMessage}" IsReadOnly="True" Width="250">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="11"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding ValidationLevel}" Value="Warning">
                                            <Setter Property="Foreground" Value="#F7630C"/>
                                            <Setter Property="Background" Value="#FFF8F1"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding ValidationLevel}" Value="Error">
                                            <Setter Property="Foreground" Value="#D13438"/>
                                            <Setter Property="Background" Value="#FDF2F2"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding ValidationLevel}" Value="None">
                                            <Setter Property="Foreground" Value="#605E5C"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTemplateColumn Header="Sales Reps" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding SalesReps, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="50"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Marketing" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding MarketingTeam, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="50"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Development" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding DevTeam, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="50"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Operations" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding OpsTeam, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="50"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Leadership" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding LeadershipTeam, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="20"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="Events Budget" Binding="{Binding EventsCost, StringFormat=C0}" Width="100"/>

                        <!-- Team Cost Breakdown Columns -->
                        <DataGridTextColumn Header="Sales Cost" Binding="{Binding SalesTeamCost, StringFormat=C0}" IsReadOnly="True" Width="90">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="11"/>
                                    <Setter Property="Foreground" Value="#605E5C"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Marketing Cost" Binding="{Binding MarketingTeamCost, StringFormat=C0}" IsReadOnly="True" Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="11"/>
                                    <Setter Property="Foreground" Value="#605E5C"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Dev Cost" Binding="{Binding DevTeamCost, StringFormat=C0}" IsReadOnly="True" Width="80">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="11"/>
                                    <Setter Property="Foreground" Value="#605E5C"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Ops Cost" Binding="{Binding OpsTeamCost, StringFormat=C0}" IsReadOnly="True" Width="80">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="11"/>
                                    <Setter Property="Foreground" Value="#605E5C"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Leadership Cost" Binding="{Binding LeadershipTeamCost, StringFormat=C0}" IsReadOnly="True" Width="110">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="11"/>
                                    <Setter Property="Foreground" Value="#605E5C"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Total Monthly Burn" Binding="{Binding TotalBurn, StringFormat=C0}" IsReadOnly="True" Width="140">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding BurnLevel}" Value="High">
                                            <Setter Property="Foreground" Value="#D13438"/>
                                            <Setter Property="Background" Value="#FDF2F2"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding BurnLevel}" Value="Medium">
                                            <Setter Property="Foreground" Value="#F7630C"/>
                                            <Setter Property="Background" Value="#FFF8F1"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding BurnLevel}" Value="Low">
                                            <Setter Property="Foreground" Value="#107C10"/>
                                            <Setter Property="Background" Value="#F3F9F3"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </ScrollViewer>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <StatusBar Grid.Row="4" Background="#F5F5F5" BorderBrush="#E1DFDD" BorderThickness="0,1,0,0">
            <StatusBarItem>
                <TextBlock x:Name="StatusText" Text="Ready" Margin="5,0"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock x:Name="FileNameText" Text="Untitled" Margin="5,0" FontWeight="SemiBold"/>
                    <TextBlock x:Name="SaveStatusText" Text="" Margin="5,0" Foreground="Orange"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>

    </Grid>
</Window>
