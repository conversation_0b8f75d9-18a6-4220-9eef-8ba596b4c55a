using System.Collections.ObjectModel;
using System.IO;
using Microsoft.Win32;
using Newtonsoft.Json;
using Planna.Models;

namespace Planna.Services;

/// <summary>
/// Service for saving and loading Planna scenarios
/// </summary>
public class ScenarioService
{
    private const string FileExtension = ".planna";
    private const string FileFilter = "Planna Scenarios (*.planna)|*.planna|All files (*.*)|*.*";
    private const string DefaultFileName = "My Scenario";
    
    public string? CurrentFilePath { get; private set; }
    public bool HasUnsavedChanges { get; set; }
    
    /// <summary>
    /// Save the current scenario to a file
    /// </summary>
    public async Task<bool> SaveScenarioAsync(BusinessLogic businessLogic, ObservableCollection<MonthlyPlanData> monthlyData, string? filePath = null)
    {
        try
        {
            // If no file path provided, show save dialog
            if (string.IsNullOrEmpty(filePath))
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = FileFilter,
                    DefaultExt = FileExtension,
                    FileName = DefaultFileName + FileExtension,
                    Title = "Save Planna Scenario"
                };
                
                if (saveDialog.ShowDialog() != true)
                    return false;
                    
                filePath = saveDialog.FileName;
            }
            
            // Create scenario object
            var scenario = CreateScenarioFromCurrentState(businessLogic, monthlyData);
            scenario.Name = Path.GetFileNameWithoutExtension(filePath);
            scenario.LastModifiedDate = DateTime.Now;
            
            // Serialize to JSON
            var json = JsonConvert.SerializeObject(scenario, Formatting.Indented);
            
            // Save to file
            await File.WriteAllTextAsync(filePath, json);
            
            CurrentFilePath = filePath;
            HasUnsavedChanges = false;
            
            return true;
        }
        catch (Exception ex)
        {
            // Log error (in a real app, use proper logging)
            System.Diagnostics.Debug.WriteLine($"Error saving scenario: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// Load a scenario from a file
    /// </summary>
    public async Task<PlannaScenario?> LoadScenarioAsync(string? filePath = null)
    {
        try
        {
            // If no file path provided, show open dialog
            if (string.IsNullOrEmpty(filePath))
            {
                var openDialog = new OpenFileDialog
                {
                    Filter = FileFilter,
                    DefaultExt = FileExtension,
                    Title = "Load Planna Scenario"
                };
                
                if (openDialog.ShowDialog() != true)
                    return null;
                    
                filePath = openDialog.FileName;
            }
            
            if (!File.Exists(filePath))
                return null;
            
            // Read and deserialize
            var json = await File.ReadAllTextAsync(filePath);
            var scenario = JsonConvert.DeserializeObject<PlannaScenario>(json);
            
            if (scenario != null)
            {
                CurrentFilePath = filePath;
                HasUnsavedChanges = false;
            }
            
            return scenario;
        }
        catch (Exception ex)
        {
            // Log error (in a real app, use proper logging)
            System.Diagnostics.Debug.WriteLine($"Error loading scenario: {ex.Message}");
            return null;
        }
    }
    
    /// <summary>
    /// Create a new scenario (reset to defaults)
    /// </summary>
    public void NewScenario()
    {
        CurrentFilePath = null;
        HasUnsavedChanges = false;
    }
    
    /// <summary>
    /// Quick save to current file path
    /// </summary>
    public async Task<bool> QuickSaveAsync(BusinessLogic businessLogic, ObservableCollection<MonthlyPlanData> monthlyData)
    {
        if (string.IsNullOrEmpty(CurrentFilePath))
        {
            return await SaveScenarioAsync(businessLogic, monthlyData);
        }
        
        return await SaveScenarioAsync(businessLogic, monthlyData, CurrentFilePath);
    }
    
    /// <summary>
    /// Convert current application state to serializable scenario
    /// </summary>
    private PlannaScenario CreateScenarioFromCurrentState(BusinessLogic businessLogic, ObservableCollection<MonthlyPlanData> monthlyData)
    {
        var scenario = new PlannaScenario
        {
            BusinessLogic = new BusinessLogicData
            {
                BasicPlan = businessLogic.BasicPlan,
                StandardPlan = businessLogic.StandardPlan,
                ProfessionalPlan = businessLogic.ProfessionalPlan,
                EnterprisePlan = businessLogic.EnterprisePlan,
                BasicMixPercent = businessLogic.BasicMixPercent,
                StandardMixPercent = businessLogic.StandardMixPercent,
                ProfessionalMixPercent = businessLogic.ProfessionalMixPercent,
                EnterpriseMixPercent = businessLogic.EnterpriseMixPercent,
                CustomersPerSalesRep = businessLogic.CustomersPerSalesRep,
                SalesRampMonths = businessLogic.SalesRampMonths,
                LeadsPerMarketer = businessLogic.LeadsPerMarketer,
                LeadConversionPercent = businessLogic.LeadConversionPercent,
                SalesRepSalary = businessLogic.SalesRepSalary,
                MarketingSalary = businessLogic.MarketingSalary,
                DeveloperSalary = businessLogic.DeveloperSalary,
                OperationsSalary = businessLogic.OperationsSalary,
                LeadershipSalary = businessLogic.LeadershipSalary,
                FixedMonthlyCosts = businessLogic.FixedMonthlyCosts,
                FreeToPayConversionPercent = businessLogic.FreeToPayConversionPercent,
                MonthlyChurnPercent = businessLogic.MonthlyChurnPercent,
                InitialCash = businessLogic.InitialCash,
                EventLeadGenerationRate = businessLogic.EventLeadGenerationRate,
                MediumEventScalingFactor = businessLogic.MediumEventScalingFactor,
                LargeEventScalingFactor = businessLogic.LargeEventScalingFactor,
                MediumEventThreshold = businessLogic.MediumEventThreshold,
                LargeEventThreshold = businessLogic.LargeEventThreshold,
                BlendedARPU = businessLogic.BlendedARPU,
                IsCustomerMixValid = businessLogic.IsCustomerMixValid
            },
            MonthlyPlans = monthlyData.Select(m => new MonthlyPlanDataSerialized
            {
                Month = m.Month,
                SalesReps = m.SalesReps,
                MarketingTeam = m.MarketingTeam,
                DevTeam = m.DevTeam,
                OpsTeam = m.OpsTeam,
                LeadershipTeam = m.LeadershipTeam,
                EventsCost = m.EventsCost
            }).ToList()
        };
        
        return scenario;
    }
    
    /// <summary>
    /// Apply loaded scenario to current application state
    /// </summary>
    public void ApplyScenarioToCurrentState(PlannaScenario scenario, BusinessLogic businessLogic, ObservableCollection<MonthlyPlanData> monthlyData)
    {
        // Apply business logic settings
        var bl = scenario.BusinessLogic;
        businessLogic.BasicPlan = bl.BasicPlan;
        businessLogic.StandardPlan = bl.StandardPlan;
        businessLogic.ProfessionalPlan = bl.ProfessionalPlan;
        businessLogic.EnterprisePlan = bl.EnterprisePlan;
        businessLogic.BasicMixPercent = bl.BasicMixPercent;
        businessLogic.StandardMixPercent = bl.StandardMixPercent;
        businessLogic.ProfessionalMixPercent = bl.ProfessionalMixPercent;
        businessLogic.EnterpriseMixPercent = bl.EnterpriseMixPercent;
        businessLogic.CustomersPerSalesRep = bl.CustomersPerSalesRep;
        businessLogic.SalesRampMonths = bl.SalesRampMonths;
        businessLogic.LeadsPerMarketer = bl.LeadsPerMarketer;
        businessLogic.LeadConversionPercent = bl.LeadConversionPercent;
        businessLogic.SalesRepSalary = bl.SalesRepSalary;
        businessLogic.MarketingSalary = bl.MarketingSalary;
        businessLogic.DeveloperSalary = bl.DeveloperSalary;
        businessLogic.OperationsSalary = bl.OperationsSalary;
        businessLogic.LeadershipSalary = bl.LeadershipSalary;
        businessLogic.FixedMonthlyCosts = bl.FixedMonthlyCosts;
        businessLogic.FreeToPayConversionPercent = bl.FreeToPayConversionPercent;
        businessLogic.MonthlyChurnPercent = bl.MonthlyChurnPercent;
        businessLogic.InitialCash = bl.InitialCash;
        businessLogic.EventLeadGenerationRate = bl.EventLeadGenerationRate;
        businessLogic.MediumEventScalingFactor = bl.MediumEventScalingFactor;
        businessLogic.LargeEventScalingFactor = bl.LargeEventScalingFactor;
        businessLogic.MediumEventThreshold = bl.MediumEventThreshold;
        businessLogic.LargeEventThreshold = bl.LargeEventThreshold;

        // Clear and rebuild monthly data
        monthlyData.Clear();
        foreach (var monthPlan in scenario.MonthlyPlans)
        {
            var monthData = new MonthlyPlanData(businessLogic)
            {
                Month = monthPlan.Month,
                SalesReps = monthPlan.SalesReps,
                MarketingTeam = monthPlan.MarketingTeam,
                DevTeam = monthPlan.DevTeam,
                OpsTeam = monthPlan.OpsTeam,
                LeadershipTeam = monthPlan.LeadershipTeam,
                EventsCost = monthPlan.EventsCost
            };
            monthlyData.Add(monthData);
        }
        
        HasUnsavedChanges = false;
    }
}
