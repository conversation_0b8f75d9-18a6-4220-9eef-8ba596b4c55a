using System.ComponentModel;

namespace Planna;

public class GrowthProjection : INotifyPropertyChanged
{
    public string Month { get; set; } = string.Empty;
    public int MonthIndex { get; set; }
    
    // Team Data
    public int SalesReps { get; set; }
    public int MarketingTeam { get; set; }
    public int DevTeam { get; set; }
    public int OpsTeam { get; set; }
    public int LeadershipTeam { get; set; }
    public decimal EventsCost { get; set; }
    
    // Calculated Metrics
    public int TotalCustomers { get; set; }
    public int NewCustomers { get; set; }
    public int ChurnedCustomers { get; set; }
    public decimal MRR { get; set; }
    public decimal ARR { get; set; }
    public decimal MonthlyBurn { get; set; }
    public decimal CashOnHand { get; set; }
    public decimal RunwayMonths { get; set; }

    // Event-based acquisition tracking
    private int _freeUsersFromEvents;
    public int FreeUsersFromEvents
    {
        get => _freeUsersFromEvents;
        set { _freeUsersFromEvents = value; OnPropertyChanged(); }
    }

    private int _newCustomersFromEvents;
    public int NewCustomersFromEvents
    {
        get => _newCustomersFromEvents;
        set { _newCustomersFromEvents = value; OnPropertyChanged(); }
    }
    
    // Unit Economics
    public decimal CAC { get; set; }
    public decimal LTV { get; set; }
    public decimal LTVToCACRatio { get; set; }
    public decimal PaybackPeriodMonths { get; set; }
    public decimal MonthlyGrowthRate { get; set; }
    public decimal GrossMargin { get; set; }
    
    // Health Indicators
    public BusinessLogic.HealthStatus LTVCACHealth { get; set; }
    public BusinessLogic.HealthStatus PaybackHealth { get; set; }
    public BusinessLogic.HealthStatus GrowthHealth { get; set; }
    public BusinessLogic.HealthStatus RunwayHealth { get; set; }
    public BusinessLogic.HealthStatus ChurnHealth { get; set; }
    
    public event PropertyChangedEventHandler? PropertyChanged;
    
    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public class GrowthProjectionEngine
{
    private readonly BusinessLogic _businessLogic;
    
    public GrowthProjectionEngine(BusinessLogic businessLogic)
    {
        _businessLogic = businessLogic;
    }
    
    public List<GrowthProjection> CalculateProjections(IEnumerable<MonthlyPlanData> monthlyPlans)
    {
        var projections = new List<GrowthProjection>();
        var currentCustomers = 0;
        var currentCash = _businessLogic.InitialCash;
        var previousMRR = 0m;
        
        foreach (var (plan, index) in monthlyPlans.Select((p, i) => (p, i)))
        {
            var projection = new GrowthProjection
            {
                Month = plan.Month,
                MonthIndex = index,
                SalesReps = plan.SalesReps,
                MarketingTeam = plan.MarketingTeam,
                DevTeam = plan.DevTeam,
                OpsTeam = plan.OpsTeam,
                LeadershipTeam = plan.LeadershipTeam,
                EventsCost = plan.EventsCost
            };
            
            // Calculate team productivity
            var effectiveSalesReps = CalculateEffectiveSalesReps(plan.SalesReps, index, _businessLogic.SalesRampMonths);
            var newCustomersFromSales = (int)(effectiveSalesReps * _businessLogic.CustomersPerSalesRep);
            
            var leadsGenerated = plan.MarketingTeam * _businessLogic.LeadsPerMarketer;
            var newCustomersFromMarketing = (int)(leadsGenerated * _businessLogic.LeadConversionPercent / 100);
            
            // Calculate event-based acquisition with scaling effectiveness
            var freeUsersFromEvents = 0;
            var newCustomersFromEvents = 0;
            if (plan.EventsCost > 0)
            {
                // Apply diminishing returns based on event spend using configurable thresholds
                decimal effectiveRate = _businessLogic.EventLeadGenerationRate;

                // Apply scaling factors based on configurable thresholds
                if (plan.EventsCost >= _businessLogic.LargeEventThreshold)
                    effectiveRate *= _businessLogic.LargeEventScalingFactor;
                else if (plan.EventsCost >= _businessLogic.MediumEventThreshold)
                    effectiveRate *= _businessLogic.MediumEventScalingFactor;

                freeUsersFromEvents = (int)(plan.EventsCost * effectiveRate / 1000);
                newCustomersFromEvents = (int)(freeUsersFromEvents * _businessLogic.FreeToPayConversionPercent / 100);
            }

            // Set event tracking properties
            projection.FreeUsersFromEvents = freeUsersFromEvents;
            projection.NewCustomersFromEvents = newCustomersFromEvents;
            
            projection.NewCustomers = newCustomersFromSales + newCustomersFromMarketing + newCustomersFromEvents;
            
            // Calculate churn
            projection.ChurnedCustomers = (int)(currentCustomers * _businessLogic.MonthlyChurnPercent / 100);
            
            // Update total customers
            currentCustomers = Math.Max(0, currentCustomers + projection.NewCustomers - projection.ChurnedCustomers);
            projection.TotalCustomers = currentCustomers;
            
            // Calculate revenue
            projection.MRR = _businessLogic.CalculateMRR(currentCustomers);
            projection.ARR = projection.MRR * 12;
            
            // Calculate costs
            projection.MonthlyBurn = _businessLogic.CalculateMonthlyBurn(plan);
            
            // Update cash
            var netCashFlow = projection.MRR - projection.MonthlyBurn;
            currentCash += netCashFlow;
            projection.CashOnHand = currentCash;
            
            // Calculate runway
            projection.RunwayMonths = _businessLogic.CalculateRunwayMonths(currentCash, projection.MonthlyBurn);
            
            // Calculate unit economics
            var totalAcquisitionCost = (plan.SalesReps * _businessLogic.SalesRepSalary) + 
                                     (plan.MarketingTeam * _businessLogic.MarketingSalary) + 
                                     plan.EventsCost;
            
            projection.CAC = _businessLogic.CalculateCustomerAcquisitionCost(
                plan.SalesReps * _businessLogic.SalesRepSalary,
                plan.MarketingTeam * _businessLogic.MarketingSalary,
                plan.EventsCost,
                projection.NewCustomers);
            
            // Calculate LTV (simplified: ARPU / churn rate)
            var avgCustomerLifeMonths = _businessLogic.MonthlyChurnPercent > 0 ? 
                100 / _businessLogic.MonthlyChurnPercent : 50; // Default to 50 months if no churn
            projection.LTV = _businessLogic.CalculateLifetimeValue(avgCustomerLifeMonths);
            
            projection.LTVToCACRatio = _businessLogic.CalculateLTVToCACRatio(projection.LTV, projection.CAC);
            projection.PaybackPeriodMonths = _businessLogic.CalculatePaybackPeriodMonths(projection.CAC, _businessLogic.BlendedARPU);
            
            // Calculate growth rate
            projection.MonthlyGrowthRate = _businessLogic.CalculateMonthlyGrowthRate(projection.MRR, previousMRR);
            previousMRR = projection.MRR;
            
            // Assume 80% gross margin for SaaS
            projection.GrossMargin = 80m;
            
            // Calculate health indicators
            projection.LTVCACHealth = BusinessLogic.GetLTVCACHealth(projection.LTVToCACRatio);
            projection.PaybackHealth = BusinessLogic.GetPaybackPeriodHealth(projection.PaybackPeriodMonths);
            projection.GrowthHealth = BusinessLogic.GetGrowthRateHealth(projection.MonthlyGrowthRate);
            projection.RunwayHealth = BusinessLogic.GetRunwayHealth(projection.RunwayMonths);
            projection.ChurnHealth = BusinessLogic.GetChurnHealth(_businessLogic.MonthlyChurnPercent);
            
            projections.Add(projection);
        }
        
        return projections;
    }
    
    private decimal CalculateEffectiveSalesReps(int totalReps, int monthIndex, int rampMonths)
    {
        // Assume reps are hired evenly over time and have a ramp period
        decimal effectiveReps = 0;
        
        for (int rep = 0; rep < totalReps; rep++)
        {
            var repStartMonth = rep * 12 / Math.Max(totalReps, 1); // Spread hiring over a year
            var monthsWorking = monthIndex - repStartMonth;
            
            if (monthsWorking <= 0)
            {
                continue; // Rep hasn't started yet
            }
            else if (monthsWorking <= rampMonths)
            {
                // Ramp up: 50% in month 1, 75% in month 2, 100% after that
                var rampPercentage = monthsWorking switch
                {
                    1 => 0.5m,
                    2 => 0.75m,
                    _ => 1.0m
                };
                effectiveReps += rampPercentage;
            }
            else
            {
                effectiveReps += 1.0m; // Fully productive
            }
        }
        
        return effectiveReps;
    }
}

