﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;
using OxyPlot;
using OxyPlot.Series;
using OxyPlot.Axes;
using OxyPlot.Annotations;
using Planna.Services;

namespace Planna;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window, INotifyPropertyChanged
{
    public ObservableCollection<MonthlyPlanData> MonthlyData { get; set; } = new();
    public BusinessLogic BusinessLogic { get; set; } = new();
    public GrowthProjectionEngine ProjectionEngine { get; set; }
    public List<GrowthProjection> CurrentProjections { get; set; } = new();

    private readonly ScenarioService _scenarioService;
    private DispatcherTimer _autoSaveTimer = new();

    public string MonthCountText => $"{MonthlyData?.Count ?? 0} months planned";

    private GrowthProjection _selectedMonthProjection = new GrowthProjection();
    public GrowthProjection SelectedMonthProjection
    {
        get => _selectedMonthProjection;
        set
        {
            _selectedMonthProjection = value;
            OnPropertyChanged();
            UpdateUnitEconomicsDisplay();
        }
    }

    public MainWindow()
    {
        InitializeComponent();
        BusinessLogic = new BusinessLogic();
        ProjectionEngine = new GrowthProjectionEngine(BusinessLogic);
        _scenarioService = new ScenarioService();
        InitializeData();
        SetupDataBinding();
        UpdateProjections();
        SetupAutoSave();
        UpdateStatusBar();
    }

    private void InitializeData()
    {
        MonthlyData = new ObservableCollection<MonthlyPlanData>
        {
            new MonthlyPlanData(BusinessLogic) { Month = "Aug '25", SalesReps = 1, MarketingTeam = 0, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 2000 },
            new MonthlyPlanData(BusinessLogic) { Month = "Sep '25", SalesReps = 2, MarketingTeam = 1, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 3500 },
            new MonthlyPlanData(BusinessLogic) { Month = "Oct '25", SalesReps = 25, MarketingTeam = 0, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 2500 }, // Should trigger warning: large sales team without marketing
            new MonthlyPlanData(BusinessLogic) { Month = "Nov '25", SalesReps = 4, MarketingTeam = 2, DevTeam = 15, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 5000 }, // Should trigger warning: large dev team without ops
            new MonthlyPlanData(BusinessLogic) { Month = "Dec '25", SalesReps = 5, MarketingTeam = 3, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 8000 },
            new MonthlyPlanData(BusinessLogic) { Month = "Jan '26", SalesReps = 8, MarketingTeam = 4, DevTeam = 3, OpsTeam = 3, LeadershipTeam = 0, EventsCost = 10000 }, // Should trigger warning: 18 people without leadership
            new MonthlyPlanData(BusinessLogic) { Month = "Feb '26", SalesReps = 5, MarketingTeam = 4, DevTeam = 0, OpsTeam = 1, LeadershipTeam = 0, EventsCost = 5000 },
            new MonthlyPlanData(BusinessLogic) { Month = "Mar '26", SalesReps = 6, MarketingTeam = 4, DevTeam = 1, OpsTeam = 1, LeadershipTeam = 0, EventsCost = 7000 },
            new MonthlyPlanData(BusinessLogic) { Month = "Apr '26", SalesReps = 7, MarketingTeam = 5, DevTeam = 1, OpsTeam = 1, LeadershipTeam = 1, EventsCost = 6000 },
            new MonthlyPlanData(BusinessLogic) { Month = "May '26", SalesReps = 8, MarketingTeam = 5, DevTeam = 2, OpsTeam = 2, LeadershipTeam = 1, EventsCost = 8000 }
        };

        MonthlyPlanningGrid.ItemsSource = MonthlyData;
    }

    private void SetupDataBinding()
    {
        // Set DataContext for the business assumptions panel
        this.DataContext = BusinessLogic;

        // Subscribe to business logic changes to update calculations and mark as changed
        BusinessLogic.PropertyChanged += (s, e) =>
        {
            UpdateCalculations();
            MarkAsChanged();
        };

        // Subscribe to monthly data changes
        foreach (var monthData in MonthlyData)
        {
            monthData.PropertyChanged += (s, e) =>
            {
                UpdateCalculations();
                MarkAsChanged();
            };
        }

        // Initialize month count display
        UpdateMonthCountDisplay();
    }

    private void UpdateCalculations()
    {
        UpdateProjections();
        CommandManager.InvalidateRequerySuggested();
    }

    private void UpdateProjections()
    {
        CurrentProjections = ProjectionEngine.CalculateProjections(MonthlyData);

        // Set the selected month to the latest month for unit economics display
        SelectedMonthProjection = CurrentProjections.LastOrDefault() ?? new GrowthProjection();

        SetupChart();
        UpdateUnitEconomicsDisplay();
    }

    private void UpdateUnitEconomicsDisplay()
    {
        // The SelectedMonthProjection property setter now handles PropertyChanged notification
        // No additional action needed here since the binding will update automatically

        // Update visual indicator on chart
        UpdateSelectedMonthIndicator();
    }

    private void SetupChart()
    {
        if (CurrentProjections == null || !CurrentProjections.Any())
            return;

        var plotModel = new PlotModel
        {
            Title = "Growth Metrics Over Time",
            Background = OxyColors.White,
            PlotAreaBorderColor = OxyColors.LightGray,
            PlotAreaBorderThickness = new OxyThickness(1)
        };

        // Add axes with better formatting
        var categoryAxis = new CategoryAxis
        {
            Position = AxisPosition.Bottom,
            Title = "Month",
            ItemsSource = CurrentProjections.Select(p => p.Month).ToArray(),
            Angle = -45,
            FontSize = 10
        };
        plotModel.Axes.Add(categoryAxis);

        var valueAxis = new LinearAxis
        {
            Position = AxisPosition.Left,
            Title = "Value",
            Minimum = 0,
            StringFormat = "N0",
            MajorGridlineStyle = LineStyle.Solid,
            MajorGridlineColor = OxyColors.LightGray,
            MinorGridlineStyle = LineStyle.Dot,
            MinorGridlineColor = OxyColors.LightGray
        };
        plotModel.Axes.Add(valueAxis);

        // Add series based on real data
        AddChartSeries(plotModel, "MRR ($)", CurrentProjections.Select(p => (double)p.MRR).ToArray(), OxyColors.Blue, true);
        AddChartSeries(plotModel, "Customers", CurrentProjections.Select(p => (double)p.TotalCustomers).ToArray(), OxyColors.Green, true);
        AddChartSeries(plotModel, "Cash on Hand ($)", CurrentProjections.Select(p => (double)p.CashOnHand).ToArray(), OxyColors.Orange, false);
        AddChartSeries(plotModel, "Monthly Burn ($)", CurrentProjections.Select(p => (double)p.MonthlyBurn).ToArray(), OxyColors.Red, false);
        AddChartSeries(plotModel, "Team Size", CurrentProjections.Select(p => (double)(p.SalesReps + p.MarketingTeam + p.DevTeam + p.OpsTeam + p.LeadershipTeam)).ToArray(), OxyColors.Purple, false);
        AddChartSeries(plotModel, "Runway (Months)", CurrentProjections.Select(p => (double)Math.Min(p.RunwayMonths, 100)).ToArray(), OxyColors.Brown, false);
        AddChartSeries(plotModel, "Free Users from Events", CurrentProjections.Select(p => (double)p.FreeUsersFromEvents).ToArray(), OxyColors.DarkOrange, false);
        AddChartSeries(plotModel, "Customers from Events", CurrentProjections.Select(p => (double)p.NewCustomersFromEvents).ToArray(), OxyColors.DarkGreen, false);

        // Add click interaction to chart
        GrowthChart.MouseDown += (s, e) =>
        {
            if (e.LeftButton == System.Windows.Input.MouseButtonState.Pressed && CurrentProjections?.Any() == true)
            {
                try
                {
                    // Get the position of the click relative to the plot area
                    var position = e.GetPosition(GrowthChart);

                    // Transform screen coordinates to data coordinates
                    var screenPoint = new ScreenPoint(position.X, position.Y);
                    var dataPoint = plotModel.Axes[0].InverseTransform(screenPoint.X);

                    // Round to nearest integer to get month index
                    var monthIndex = (int)Math.Round(dataPoint);

                    // Ensure the index is within bounds
                    if (monthIndex >= 0 && monthIndex < CurrentProjections.Count)
                    {
                        SelectedMonthProjection = CurrentProjections[monthIndex];
                    }
                }
                catch
                {
                    // Fallback to cycling behavior if coordinate transformation fails
                    var currentIndex = CurrentProjections.IndexOf(SelectedMonthProjection);
                    var nextIndex = (currentIndex + 1) % CurrentProjections.Count;
                    SelectedMonthProjection = CurrentProjections[nextIndex];
                }
            }
        };

        // Enable zooming and panning with better UX
        plotModel.Axes.ToList().ForEach(axis =>
        {
            axis.IsZoomEnabled = true;
            axis.IsPanEnabled = true;
        });

        GrowthChart.Model = plotModel;

        // Add visual indicator for selected month
        UpdateSelectedMonthIndicator();

        GrowthChart.InvalidatePlot(true);
    }

    private void AddChartSeries(PlotModel plotModel, string title, double[] data, OxyColor color, bool isVisible)
    {
        var series = new LineSeries
        {
            Title = title,
            Color = color,
            StrokeThickness = 3,
            MarkerType = MarkerType.Circle,
            MarkerSize = 5,
            MarkerFill = color,
            MarkerStroke = OxyColors.White,
            MarkerStrokeThickness = 1,
            IsVisible = isVisible
        };

        for (int i = 0; i < data.Length; i++)
        {
            series.Points.Add(new DataPoint(i, data[i]));
        }

        plotModel.Series.Add(series);
    }

    private void MetricToggle_Changed(object sender, RoutedEventArgs e)
    {
        if (GrowthChart?.Model != null)
        {
            var plotModel = GrowthChart.Model;

            // Update series visibility based on toggle states
            foreach (var series in plotModel.Series.OfType<LineSeries>())
            {
                series.IsVisible = series.Title switch
                {
                    "MRR ($)" => MRRToggle?.IsChecked == true,
                    "Customers" => CustomersToggle?.IsChecked == true,
                    "Cash on Hand ($)" => CashToggle?.IsChecked == true,
                    "Monthly Burn ($)" => BurnToggle?.IsChecked == true,
                    "Team Size" => TeamToggle?.IsChecked == true,
                    "Runway (Months)" => RunwayToggle?.IsChecked == true,
                    "Free Users from Events" => FreeUsersEventsToggle?.IsChecked == true,
                    "Customers from Events" => CustomersEventsToggle?.IsChecked == true,
                    _ => series.IsVisible
                };
            }

            // Refresh the chart
            GrowthChart.InvalidatePlot(true);
        }
    }

    private void CopyPreviousMonth_Click(object sender, RoutedEventArgs e)
    {
        if (MonthlyData.Count < 2) return;

        var result = MessageBox.Show(
            "Copy team sizes from the previous month to all subsequent months?",
            "Copy Previous Month",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            try
            {
                // Temporarily disable property change notifications to avoid cascading updates
                for (int i = 1; i < MonthlyData.Count; i++)
                {
                    var current = MonthlyData[i];
                    var previous = MonthlyData[i - 1];

                    // Debug output to see what values we're copying
                    System.Diagnostics.Debug.WriteLine($"Copying from {previous.Month} to {current.Month}:");
                    System.Diagnostics.Debug.WriteLine($"  Before - SalesReps: {current.SalesReps}, EventsCost: {current.EventsCost}");
                    System.Diagnostics.Debug.WriteLine($"  Source - SalesReps: {previous.SalesReps}, EventsCost: {previous.EventsCost}");

                    // Copy all values
                    current.SalesReps = previous.SalesReps;
                    current.MarketingTeam = previous.MarketingTeam;
                    current.DevTeam = previous.DevTeam;
                    current.OpsTeam = previous.OpsTeam;
                    current.LeadershipTeam = previous.LeadershipTeam;
                    current.EventsCost = previous.EventsCost;

                    System.Diagnostics.Debug.WriteLine($"  After - SalesReps: {current.SalesReps}, EventsCost: {current.EventsCost}");
                }

                // Force UI refresh
                UpdateCalculations();

                // Force DataGrid refresh
                MonthlyPlanningGrid.Items.Refresh();

                MessageBox.Show("Copy completed successfully!", "Copy Previous", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during copy: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"Copy error: {ex}");
            }
        }
    }

    private void ApplyTemplate_Click(object sender, RoutedEventArgs e)
    {
        // Simple template selection using MessageBox for now
        var result = MessageBox.Show(
            "Choose a growth template:\n\n" +
            "YES = Conservative Growth (10% monthly)\n" +
            "NO = Aggressive Growth (25% monthly)\n" +
            "CANCEL = Startup Pattern (sales-first)",
            "Select Growth Template",
            MessageBoxButton.YesNoCancel,
            MessageBoxImage.Question);

        switch (result)
        {
            case MessageBoxResult.Yes:
                ApplyGrowthTemplate("Conservative Growth");
                break;
            case MessageBoxResult.No:
                ApplyGrowthTemplate("Aggressive Growth");
                break;
            case MessageBoxResult.Cancel:
                ApplyGrowthTemplate("Startup Pattern");
                break;
        }
    }

    private void AddMonth_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var lastMonth = MonthlyData.LastOrDefault();
            if (lastMonth == null)
            {
                MessageBox.Show("Cannot add month: No existing months found.", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Limit the number of months to prevent performance issues
            if (MonthlyData.Count >= 24)
            {
                var result = MessageBox.Show(
                    "You already have 24 months planned. Adding more months may impact performance.\n\nDo you want to continue?",
                    "Add Month",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes)
                    return;
            }

            var newMonth = new MonthlyPlanData(BusinessLogic)
            {
                Month = GetNextMonthName(lastMonth.Month),
                SalesReps = lastMonth.SalesReps,
                MarketingTeam = lastMonth.MarketingTeam,
                DevTeam = lastMonth.DevTeam,
                OpsTeam = lastMonth.OpsTeam,
                LeadershipTeam = lastMonth.LeadershipTeam,
                EventsCost = lastMonth.EventsCost
            };

            // Subscribe to property changes before adding to collection
            newMonth.PropertyChanged += (s, e) =>
            {
                UpdateCalculations();
                MarkAsChanged();
            };

            // Add to collection
            MonthlyData.Add(newMonth);

            // Update calculations and UI
            UpdateCalculations();

            // Update the month count display
            UpdateMonthCountDisplay();

            // Scroll to the new month if possible
            if (MonthlyPlanningGrid.Items.Count > 0)
            {
                MonthlyPlanningGrid.ScrollIntoView(MonthlyPlanningGrid.Items[MonthlyPlanningGrid.Items.Count - 1]);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error adding month: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void TestValidation_Click(object sender, RoutedEventArgs e)
    {
        if (MonthlyData.Count > 0)
        {
            var firstMonth = MonthlyData[0];

            // Set extreme values to trigger validation
            firstMonth.SalesReps = 25;  // Should trigger "large sales team without marketing"
            firstMonth.MarketingTeam = 0;
            firstMonth.DevTeam = 0;
            firstMonth.OpsTeam = 0;
            firstMonth.LeadershipTeam = 0;

            MessageBox.Show("Applied test values to first month. Check the Feedback column!", "Test Validation", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void RemoveMonth_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (MonthlyData.Count <= 1)
            {
                MessageBox.Show("Cannot remove month: At least one month must remain.", "Remove Month", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"Remove the last month ({MonthlyData.Last().Month}) from planning?",
                "Remove Month",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                var lastMonth = MonthlyData.Last();

                // Unsubscribe from property changes to prevent memory leaks
                lastMonth.PropertyChanged -= (s, e) => UpdateCalculations();

                // Remove from collection
                MonthlyData.RemoveAt(MonthlyData.Count - 1);

                // Update calculations and UI
                UpdateCalculations();
                UpdateMonthCountDisplay();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error removing month: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ApplyGrowthTemplate(string template)
    {
        switch (template)
        {
            case "Conservative Growth":
                ApplyConservativeGrowth();
                break;
            case "Aggressive Growth":
                ApplyAggressiveGrowth();
                break;
            case "Startup Pattern":
                ApplyStartupGrowth();
                break;
        }
    }

    private void ApplyConservativeGrowth()
    {
        for (int i = 1; i < MonthlyData.Count; i++)
        {
            var current = MonthlyData[i];
            var previous = MonthlyData[i - 1];

            // 10% monthly growth
            current.SalesReps = Math.Max(previous.SalesReps, (int)(previous.SalesReps * 1.1));
            current.MarketingTeam = Math.Max(previous.MarketingTeam, (int)(previous.MarketingTeam * 1.05));
            current.DevTeam = previous.DevTeam + (i % 3 == 0 ? 1 : 0); // Add dev every 3 months
            current.OpsTeam = previous.OpsTeam + (i % 4 == 0 ? 1 : 0); // Add ops every 4 months
            current.LeadershipTeam = previous.LeadershipTeam + (i % 6 == 0 ? 1 : 0); // Add leadership every 6 months
        }
    }

    private void ApplyAggressiveGrowth()
    {
        for (int i = 1; i < MonthlyData.Count; i++)
        {
            var current = MonthlyData[i];
            var previous = MonthlyData[i - 1];

            // 25% monthly growth
            current.SalesReps = Math.Max(previous.SalesReps + 1, (int)(previous.SalesReps * 1.25));
            current.MarketingTeam = Math.Max(previous.MarketingTeam, (int)(previous.MarketingTeam * 1.15));
            current.DevTeam = previous.DevTeam + (i % 2 == 0 ? 1 : 0); // Add dev every 2 months
            current.OpsTeam = previous.OpsTeam + (i % 3 == 0 ? 1 : 0); // Add ops every 3 months
            current.LeadershipTeam = previous.LeadershipTeam + (i % 4 == 0 ? 1 : 0); // Add leadership every 4 months
        }
    }

    private void ApplyStartupGrowth()
    {
        for (int i = 1; i < MonthlyData.Count; i++)
        {
            var current = MonthlyData[i];
            var previous = MonthlyData[i - 1];

            // Startup pattern: focus on sales first, then support
            if (i <= 3) // First 3 months: sales focus
            {
                current.SalesReps = previous.SalesReps + 1;
                current.MarketingTeam = Math.Max(1, previous.MarketingTeam);
                current.DevTeam = previous.DevTeam;
                current.OpsTeam = previous.OpsTeam;
                current.LeadershipTeam = previous.LeadershipTeam;
            }
            else if (i <= 6) // Months 4-6: add support
            {
                current.SalesReps = previous.SalesReps + 1;
                current.MarketingTeam = previous.MarketingTeam + (i % 2 == 0 ? 1 : 0);
                current.DevTeam = previous.DevTeam + (i == 4 ? 1 : 0);
                current.OpsTeam = previous.OpsTeam + (i == 5 ? 1 : 0);
                current.LeadershipTeam = previous.LeadershipTeam;
            }
            else // Months 7+: balanced growth
            {
                current.SalesReps = previous.SalesReps + 1;
                current.MarketingTeam = previous.MarketingTeam + (i % 2 == 0 ? 1 : 0);
                current.DevTeam = previous.DevTeam + (i % 3 == 0 ? 1 : 0);
                current.OpsTeam = previous.OpsTeam + (i % 4 == 0 ? 1 : 0);
                current.LeadershipTeam = previous.LeadershipTeam + (i % 6 == 0 ? 1 : 0);
            }
        }
    }

    private string GetNextMonthName(string currentMonth)
    {
        try
        {
            var months = new[] { "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec" };
            var parts = currentMonth.Split(' ');

            if (parts.Length == 2)
            {
                var monthName = parts[0];
                var yearPart = parts[1].Replace("'", ""); // Remove apostrophe if present

                var currentIndex = Array.IndexOf(months, monthName);
                if (currentIndex >= 0 && int.TryParse(yearPart, out int year))
                {
                    var nextIndex = (currentIndex + 1) % 12;
                    var nextYear = nextIndex == 0 ? year + 1 : year;

                    // Format year as 2-digit if it's a 4-digit year
                    var yearString = nextYear > 99 ? (nextYear % 100).ToString("00") : nextYear.ToString("00");

                    return $"{months[nextIndex]} '{yearString}";
                }
            }
        }
        catch (Exception ex)
        {
            // Log error if needed, but don't crash the app
            System.Diagnostics.Debug.WriteLine($"Error generating next month name: {ex.Message}");
        }

        // Fallback: generate a simple sequential name
        return $"Month {MonthlyData.Count + 1}";
    }

    private void UpdateSelectedMonthIndicator()
    {
        if (GrowthChart?.Model == null || CurrentProjections == null || !CurrentProjections.Any())
            return;

        var plotModel = GrowthChart.Model;

        // Remove existing selection indicator
        var existingAnnotation = plotModel.Annotations.FirstOrDefault(a => a.Tag?.ToString() == "SelectedMonth");
        if (existingAnnotation != null)
        {
            plotModel.Annotations.Remove(existingAnnotation);
        }

        // Find the index of the selected month
        var selectedIndex = CurrentProjections.IndexOf(SelectedMonthProjection);
        if (selectedIndex >= 0)
        {
            // Add a vertical line annotation to highlight the selected month
            var lineAnnotation = new LineAnnotation
            {
                Type = LineAnnotationType.Vertical,
                X = selectedIndex,
                Color = OxyColors.Red,
                StrokeThickness = 2,
                LineStyle = LineStyle.Dash,
                Tag = "SelectedMonth"
            };

            plotModel.Annotations.Add(lineAnnotation);

            // Refresh the chart
            GrowthChart.InvalidatePlot(false);
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    private void UpdateMonthCountDisplay()
    {
        // Update the TextBlock directly
        if (MonthCountTextBlock != null)
        {
            MonthCountTextBlock.Text = MonthCountText;
        }
    }

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    #region Save/Load Functionality

    private void SetupAutoSave()
    {
        _autoSaveTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(30) // Auto-save every 30 seconds
        };
        _autoSaveTimer.Tick += AutoSave_Tick;
        _autoSaveTimer.Start();
    }

    private async void AutoSave_Tick(object? sender, EventArgs e)
    {
        if (_scenarioService.HasUnsavedChanges && !string.IsNullOrEmpty(_scenarioService.CurrentFilePath))
        {
            await _scenarioService.QuickSaveAsync(BusinessLogic, MonthlyData);
            UpdateStatusBar();
        }
    }

    private async void NewScenario_Click(object sender, RoutedEventArgs e)
    {
        if (_scenarioService.HasUnsavedChanges)
        {
            var result = MessageBox.Show(
                "You have unsaved changes. Do you want to save before creating a new scenario?",
                "Unsaved Changes",
                MessageBoxButton.YesNoCancel,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                var saved = await _scenarioService.SaveScenarioAsync(BusinessLogic, MonthlyData);
                if (!saved) return; // User cancelled save dialog
            }
            else if (result == MessageBoxResult.Cancel)
            {
                return; // User cancelled the new scenario operation
            }
        }

        // Reset to default state
        _scenarioService.NewScenario();
        BusinessLogic = new BusinessLogic();
        ProjectionEngine = new GrowthProjectionEngine(BusinessLogic);
        InitializeData();
        SetupDataBinding();
        UpdateProjections();
        UpdateStatusBar();

        StatusText.Text = "New scenario created";
    }

    private async void LoadScenario_Click(object sender, RoutedEventArgs e)
    {
        if (_scenarioService.HasUnsavedChanges)
        {
            var result = MessageBox.Show(
                "You have unsaved changes. Do you want to save before loading a scenario?",
                "Unsaved Changes",
                MessageBoxButton.YesNoCancel,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                var saved = await _scenarioService.SaveScenarioAsync(BusinessLogic, MonthlyData);
                if (!saved) return; // User cancelled save dialog
            }
            else if (result == MessageBoxResult.Cancel)
            {
                return; // User cancelled the load operation
            }
        }

        var scenario = await _scenarioService.LoadScenarioAsync();
        if (scenario != null)
        {
            _scenarioService.ApplyScenarioToCurrentState(scenario, BusinessLogic, MonthlyData);

            // Rebind data and update UI
            SetupDataBinding();
            UpdateProjections();
            UpdateStatusBar();

            StatusText.Text = $"Loaded scenario: {scenario.Name}";
        }
    }

    private async void SaveScenario_Click(object sender, RoutedEventArgs e)
    {
        var success = await _scenarioService.QuickSaveAsync(BusinessLogic, MonthlyData);
        if (success)
        {
            UpdateStatusBar();
            StatusText.Text = "Scenario saved successfully";
        }
        else
        {
            StatusText.Text = "Failed to save scenario";
        }
    }

    private async void SaveAsScenario_Click(object sender, RoutedEventArgs e)
    {
        var success = await _scenarioService.SaveScenarioAsync(BusinessLogic, MonthlyData);
        if (success)
        {
            UpdateStatusBar();
            StatusText.Text = "Scenario saved successfully";
        }
        else
        {
            StatusText.Text = "Failed to save scenario";
        }
    }

    private void UpdateStatusBar()
    {
        if (_scenarioService.CurrentFilePath != null)
        {
            FileNameText.Text = System.IO.Path.GetFileNameWithoutExtension(_scenarioService.CurrentFilePath);
        }
        else
        {
            FileNameText.Text = "Untitled";
        }

        SaveStatusText.Text = _scenarioService.HasUnsavedChanges ? "●" : "";
    }

    private void MarkAsChanged()
    {
        _scenarioService.HasUnsavedChanges = true;
        UpdateStatusBar();
    }

    #endregion
}