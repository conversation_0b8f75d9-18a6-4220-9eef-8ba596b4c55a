using System.ComponentModel;

namespace Planna;

public class MonthlyPlanData : INotifyPropertyChanged
{
    private readonly BusinessLogic _businessLogic;
    private int _salesReps;
    private int _marketingTeam;
    private int _devTeam;
    private int _opsTeam;
    private int _leadershipTeam;
    private decimal _eventsCost;

    public MonthlyPlanData(BusinessLogic businessLogic)
    {
        _businessLogic = businessLogic;
    }

    public string Month { get; set; } = string.Empty;

    public int SalesReps
    {
        get => _salesReps;
        set
        {
            _salesReps = value;
            OnPropertyChanged(nameof(SalesReps));
            UpdateTotalBurn();
        }
    }

    public int MarketingTeam
    {
        get => _marketingTeam;
        set
        {
            _marketingTeam = value;
            OnPropertyChanged(nameof(MarketingTeam));
            UpdateTotalBurn();
        }
    }

    public int DevTeam
    {
        get => _devTeam;
        set
        {
            _devTeam = value;
            OnPropertyChanged(nameof(DevTeam));
            UpdateTotalBurn();
        }
    }

    public int OpsTeam
    {
        get => _opsTeam;
        set
        {
            _opsTeam = value;
            OnPropertyChanged(nameof(OpsTeam));
            UpdateTotalBurn();
        }
    }

    public int LeadershipTeam
    {
        get => _leadershipTeam;
        set
        {
            _leadershipTeam = value;
            OnPropertyChanged(nameof(LeadershipTeam));
            UpdateTotalBurn();
        }
    }

    public decimal EventsCost
    {
        get => _eventsCost;
        set
        {
            _eventsCost = value;
            OnPropertyChanged(nameof(EventsCost));
            UpdateTotalBurn();
        }
    }

    public decimal TotalBurn { get; private set; }
    public string BurnLevel { get; private set; } = "Low";

    // Individual team costs for breakdown visualization
    public decimal SalesTeamCost { get; private set; }
    public decimal MarketingTeamCost { get; private set; }
    public decimal DevTeamCost { get; private set; }
    public decimal OpsTeamCost { get; private set; }
    public decimal LeadershipTeamCost { get; private set; }
    public decimal FixedCosts { get; private set; }

    // Validation and feedback properties
    public string ValidationMessage { get; private set; } = "";
    public string ValidationLevel { get; private set; } = "None"; // None, Warning, Error
    public bool HasValidationIssues => ValidationLevel != "None";

    private void UpdateTotalBurn()
    {
        // Calculate individual team costs
        SalesTeamCost = _salesReps * _businessLogic.SalesRepSalary;
        MarketingTeamCost = _marketingTeam * _businessLogic.MarketingSalary;
        DevTeamCost = _devTeam * _businessLogic.DeveloperSalary;
        OpsTeamCost = _opsTeam * _businessLogic.OperationsSalary;
        LeadershipTeamCost = _leadershipTeam * _businessLogic.LeadershipSalary;
        FixedCosts = _businessLogic.FixedMonthlyCosts;

        TotalBurn = _businessLogic.CalculateMonthlyBurn(this);

        BurnLevel = TotalBurn switch
        {
            > 50000 => "High",
            > 30000 => "Medium",
            _ => "Low"
        };

        // Notify all cost-related properties
        OnPropertyChanged(nameof(SalesTeamCost));
        OnPropertyChanged(nameof(MarketingTeamCost));
        OnPropertyChanged(nameof(DevTeamCost));
        OnPropertyChanged(nameof(OpsTeamCost));
        OnPropertyChanged(nameof(LeadershipTeamCost));
        OnPropertyChanged(nameof(FixedCosts));
        OnPropertyChanged(nameof(TotalBurn));
        OnPropertyChanged(nameof(BurnLevel));

        // Perform validation
        ValidateTeamStructure();
    }

    private void ValidateTeamStructure()
    {
        var totalTeam = _salesReps + _marketingTeam + _devTeam + _opsTeam + _leadershipTeam;

        // Reset validation
        ValidationMessage = "";
        ValidationLevel = "None";

        // Debug output
        System.Diagnostics.Debug.WriteLine($"Validating {Month}: Sales={_salesReps}, Marketing={_marketingTeam}, Dev={_devTeam}, Ops={_opsTeam}, Leadership={_leadershipTeam}, Total={totalTeam}, Burn={TotalBurn}");

        // Check for unrealistic growth patterns
        if (totalTeam > 50)
        {
            ValidationMessage = "⚠️ Large team size may indicate unrealistic growth";
            ValidationLevel = "Warning";
        }
        else if (_salesReps > 20 && _marketingTeam == 0)
        {
            ValidationMessage = "⚠️ Consider adding marketing support for large sales team";
            ValidationLevel = "Warning";
        }
        else if (_devTeam > 10 && _opsTeam == 0)
        {
            ValidationMessage = "⚠️ Large dev team may need operations support";
            ValidationLevel = "Warning";
        }
        else if (totalTeam > 15 && _leadershipTeam == 0)
        {
            ValidationMessage = "⚠️ Consider adding leadership for team management";
            ValidationLevel = "Warning";
        }
        else if (TotalBurn > 100000)
        {
            ValidationMessage = "🚨 Very high burn rate - review team structure";
            ValidationLevel = "Error";
        }
        else if (totalTeam > 0)
        {
            // Show a simple status message for testing
            ValidationMessage = $"✓ Team structure looks good ({totalTeam} people)";
            ValidationLevel = "None";
        }

        // Debug output for validation result
        System.Diagnostics.Debug.WriteLine($"Validation result for {Month}: Level={ValidationLevel}, Message='{ValidationMessage}'");

        // Notify validation properties
        OnPropertyChanged(nameof(ValidationMessage));
        OnPropertyChanged(nameof(ValidationLevel));
        OnPropertyChanged(nameof(HasValidationIssues));
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
