﻿<Application x:Class="Planna.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:Planna"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Indigo" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Modern professional color scheme inspired by HeroUI -->
            <SolidColorBrush x:Key="AccentBrush" Color="#0070F3"/>
            <SolidColorBrush x:Key="AccentLightBrush" Color="#3291FF"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="BorderBrush" Color="#E4E4E7"/>
            <SolidColorBrush x:Key="TextPrimaryBrush" Color="#09090B"/>
            <SolidColorBrush x:Key="TextSecondaryBrush" Color="#71717A"/>

            <!-- Health Status Converters -->
            <local:HealthToColorConverter x:Key="HealthToColorConverter"/>
            <local:HealthToIconConverter x:Key="HealthToIconConverter"/>
            <local:HealthToMessageConverter x:Key="HealthToMessageConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
