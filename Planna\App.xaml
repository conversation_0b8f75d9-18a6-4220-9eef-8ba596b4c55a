﻿<Application x:Class="Planna.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:Planna"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <!-- Modern color scheme -->
        <SolidColorBrush x:Key="AccentBrush" Color="#0078D4"/>
        <SolidColorBrush x:Key="AccentLightBrush" Color="#106EBE"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="#F3F2F1"/>

        <!-- Health Status Converters -->
        <local:HealthToColorConverter x:Key="HealthToColorConverter"/>
        <local:HealthToIconConverter x:Key="HealthToIconConverter"/>
        <local:HealthToMessageConverter x:Key="HealthToMessageConverter"/>
    </Application.Resources>
</Application>
