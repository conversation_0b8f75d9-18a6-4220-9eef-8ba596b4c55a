﻿#pragma checksum "..\..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3E6B6100F9D0E5F5BF0ABDEBEF90BA29A7C390E4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using OxyPlot.Wpf;
using Planna;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Planna {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 315 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton MRRToggle;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CustomersToggle;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CashToggle;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton BurnToggle;
        
        #line default
        #line hidden
        
        
        #line 327 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton TeamToggle;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RunwayToggle;
        
        #line default
        #line hidden
        
        
        #line 333 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton FreeUsersEventsToggle;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CustomersEventsToggle;
        
        #line default
        #line hidden
        
        
        #line 342 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal OxyPlot.Wpf.PlotView GrowthChart;
        
        #line default
        #line hidden
        
        
        #line 580 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 602 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid MonthlyPlanningGrid;
        
        #line default
        #line hidden
        
        
        #line 814 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 818 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNameText;
        
        #line default
        #line hidden
        
        
        #line 819 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SaveStatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Planna;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 54 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NewScenario_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 56 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LoadScenario_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 58 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveScenario_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 60 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveAsScenario_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.MRRToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 317 "..\..\..\..\MainWindow.xaml"
            this.MRRToggle.Checked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            
            #line 317 "..\..\..\..\MainWindow.xaml"
            this.MRRToggle.Unchecked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CustomersToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 320 "..\..\..\..\MainWindow.xaml"
            this.CustomersToggle.Checked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            
            #line 320 "..\..\..\..\MainWindow.xaml"
            this.CustomersToggle.Unchecked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            return;
            case 7:
            this.CashToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 323 "..\..\..\..\MainWindow.xaml"
            this.CashToggle.Checked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            
            #line 323 "..\..\..\..\MainWindow.xaml"
            this.CashToggle.Unchecked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BurnToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 326 "..\..\..\..\MainWindow.xaml"
            this.BurnToggle.Checked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            
            #line 326 "..\..\..\..\MainWindow.xaml"
            this.BurnToggle.Unchecked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            return;
            case 9:
            this.TeamToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 329 "..\..\..\..\MainWindow.xaml"
            this.TeamToggle.Checked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            
            #line 329 "..\..\..\..\MainWindow.xaml"
            this.TeamToggle.Unchecked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            return;
            case 10:
            this.RunwayToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 332 "..\..\..\..\MainWindow.xaml"
            this.RunwayToggle.Checked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            
            #line 332 "..\..\..\..\MainWindow.xaml"
            this.RunwayToggle.Unchecked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            return;
            case 11:
            this.FreeUsersEventsToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 335 "..\..\..\..\MainWindow.xaml"
            this.FreeUsersEventsToggle.Checked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            
            #line 335 "..\..\..\..\MainWindow.xaml"
            this.FreeUsersEventsToggle.Unchecked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CustomersEventsToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 338 "..\..\..\..\MainWindow.xaml"
            this.CustomersEventsToggle.Checked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            
            #line 338 "..\..\..\..\MainWindow.xaml"
            this.CustomersEventsToggle.Unchecked += new System.Windows.RoutedEventHandler(this.MetricToggle_Changed);
            
            #line default
            #line hidden
            return;
            case 13:
            this.GrowthChart = ((OxyPlot.Wpf.PlotView)(target));
            return;
            case 14:
            this.MonthCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            
            #line 584 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyPreviousMonth_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 587 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyTemplate_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 590 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveMonth_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 593 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestValidation_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 595 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddMonth_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.MonthlyPlanningGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 21:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.FileNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.SaveStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

