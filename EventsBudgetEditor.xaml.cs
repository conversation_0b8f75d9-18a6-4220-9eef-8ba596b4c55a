using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Planna;

public partial class EventsBudgetEditor : UserControl
{
    private bool _isUpdatingTextBox = false;

    public static readonly DependencyProperty ValueProperty =
        DependencyProperty.Register("Value", typeof(decimal), typeof(EventsBudgetEditor),
            new FrameworkPropertyMetadata(0m, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnValueChanged));

    public decimal Value
    {
        get { return (decimal)GetValue(ValueProperty); }
        set { SetValue(ValueProperty, value); }
    }

    public static readonly DependencyProperty MinimumProperty =
        DependencyProperty.Register("Minimum", typeof(decimal), typeof(EventsBudgetEditor),
            new PropertyMetadata(0m));

    public decimal Minimum
    {
        get { return (decimal)GetValue(MinimumProperty); }
        set { SetValue(MinimumProperty, value); }
    }

    public static readonly DependencyProperty MaximumProperty =
        DependencyProperty.Register("Maximum", typeof(decimal), typeof(EventsBudgetEditor),
            new PropertyMetadata(100000m));

    public decimal Maximum
    {
        get { return (decimal)GetValue(MaximumProperty); }
        set { SetValue(MaximumProperty, value); }
    }

    public EventsBudgetEditor()
    {
        InitializeComponent();
        BudgetTextBox.TextChanged += BudgetTextBox_TextChanged;
        BudgetTextBox.LostFocus += BudgetTextBox_LostFocus;
        BudgetTextBox.KeyDown += BudgetTextBox_KeyDown;
        BudgetTextBox.PreviewKeyDown += BudgetTextBox_PreviewKeyDown;
    }

    private static void OnValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is EventsBudgetEditor editor && !editor._isUpdatingTextBox)
        {
            editor._isUpdatingTextBox = true;
            editor.BudgetTextBox.Text = ((decimal)e.NewValue).ToString("N0");
            editor._isUpdatingTextBox = false;
        }
    }

    private void BudgetTextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        if (_isUpdatingTextBox) return;

        var text = BudgetTextBox.Text.Replace(",", "").Replace("$", "");
        if (decimal.TryParse(text, out decimal newValue))
        {
            newValue = Math.Max(Minimum, Math.Min(Maximum, newValue));
            if (newValue != Value)
            {
                Value = newValue;
            }
        }
    }

    private void BudgetTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        // Ensure the text box shows the current valid value
        if (!_isUpdatingTextBox)
        {
            _isUpdatingTextBox = true;
            BudgetTextBox.Text = Value.ToString("N0");
            _isUpdatingTextBox = false;
        }
    }

    private void BudgetTextBox_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            BudgetTextBox_LostFocus(sender, e);
            Keyboard.ClearFocus(); // Remove focus from the textbox
        }
    }

    private void BudgetTextBox_PreviewKeyDown(object sender, KeyEventArgs e)
    {
        // Handle keyboard shortcuts
        if (Keyboard.Modifiers == ModifierKeys.Control)
        {
            if (e.Key == Key.Up && Value < Maximum)
            {
                Value = Math.Min(Maximum, Value + 1000);
                e.Handled = true;
            }
            else if (e.Key == Key.Down && Value > Minimum)
            {
                Value = Math.Max(Minimum, Value - 1000);
                e.Handled = true;
            }
        }
        // Only allow numeric input and navigation keys
        else if (!IsNumericKey(e.Key))
        {
            e.Handled = true;
        }
    }

    private bool IsNumericKey(Key key)
    {
        return (key >= Key.D0 && key <= Key.D9) ||
               (key >= Key.NumPad0 && key <= Key.NumPad9) ||
               key == Key.Back || key == Key.Delete ||
               key == Key.Left || key == Key.Right ||
               key == Key.Tab || key == Key.Enter ||
               key == Key.OemComma; // Allow comma for thousands separator
    }

    private void QuickAmount_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string tagValue)
        {
            if (decimal.TryParse(tagValue, out decimal amount))
            {
                Value = Math.Max(Minimum, Math.Min(Maximum, amount));
            }
        }
    }
}
