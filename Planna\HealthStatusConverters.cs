using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace Planna;

public class HealthToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is BusinessLogic.HealthStatus status)
        {
            var colorString = BusinessLogic.GetHealthColor(status);
            return (Color)ColorConverter.ConvertFromString(colorString);
        }
        return Colors.Gray;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class HealthToIconConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is BusinessLogic.HealthStatus status)
        {
            return status switch
            {
                BusinessLogic.HealthStatus.Excellent => "🎉",
                BusinessLogic.HealthStatus.Good => "✅",
                BusinessLogic.HealthStatus.Acceptable => "⚠️",
                BusinessLogic.HealthStatus.Poor => "🔶",
                BusinessLogic.HealthStatus.Critical => "🚨",
                _ => "❓"
            };
        }
        return "❓";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class HealthToMessageConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is BusinessLogic.HealthStatus status && parameter is string metricName)
        {
            return BusinessLogic.GetHealthMessage(status, metricName);
        }
        return "Unknown status";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
