using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Planna;

public partial class TeamSizeEditor : UserControl
{
    private bool _isUpdatingTextBox = false;

    public static readonly DependencyProperty ValueProperty =
        DependencyProperty.Register("Value", typeof(int), typeof(TeamSizeEditor),
            new FrameworkPropertyMetadata(0, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnValueChanged));

    public int Value
    {
        get { return (int)GetValue(ValueProperty); }
        set { SetValue(ValueProperty, value); }
    }

    public static readonly DependencyProperty MinimumProperty =
        DependencyProperty.Register("Minimum", typeof(int), typeof(TeamSizeEditor),
            new PropertyMetadata(0));

    public int Minimum
    {
        get { return (int)GetValue(MinimumProperty); }
        set { SetValue(MinimumProperty, value); }
    }

    public static readonly DependencyProperty MaximumProperty =
        DependencyProperty.Register("Maximum", typeof(int), typeof(TeamSizeEditor),
            new PropertyMetadata(100));

    public int Maximum
    {
        get { return (int)GetValue(MaximumProperty); }
        set { SetValue(MaximumProperty, value); }
    }

    public TeamSizeEditor()
    {
        InitializeComponent();
        ValueTextBox.TextChanged += ValueTextBox_TextChanged;
        ValueTextBox.LostFocus += ValueTextBox_LostFocus;
        ValueTextBox.KeyDown += ValueTextBox_KeyDown;
        ValueTextBox.PreviewKeyDown += ValueTextBox_PreviewKeyDown;

        // Enable button states based on min/max values
        UpdateButtonStates();
    }

    private static void OnValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is TeamSizeEditor editor && !editor._isUpdatingTextBox)
        {
            editor._isUpdatingTextBox = true;
            editor.ValueTextBox.Text = e.NewValue.ToString();
            editor._isUpdatingTextBox = false;
            editor.UpdateButtonStates();
        }
    }

    private void ValueTextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        if (_isUpdatingTextBox) return;

        if (int.TryParse(ValueTextBox.Text, out int newValue))
        {
            newValue = Math.Max(Minimum, Math.Min(Maximum, newValue));
            if (newValue != Value)
            {
                Value = newValue;
            }
        }
    }

    private void ValueTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        // Ensure the text box shows the current valid value
        if (!_isUpdatingTextBox)
        {
            _isUpdatingTextBox = true;
            ValueTextBox.Text = Value.ToString();
            _isUpdatingTextBox = false;
        }
    }

    private void ValueTextBox_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            ValueTextBox_LostFocus(sender, e);
            Keyboard.ClearFocus(); // Remove focus from the textbox
        }
    }

    private void ValueTextBox_PreviewKeyDown(object sender, KeyEventArgs e)
    {
        // Handle keyboard shortcuts
        if (Keyboard.Modifiers == ModifierKeys.Control)
        {
            if (e.Key == Key.Up && Value < Maximum)
            {
                Value++;
                e.Handled = true;
            }
            else if (e.Key == Key.Down && Value > Minimum)
            {
                Value--;
                e.Handled = true;
            }
        }
        // Handle arrow keys for increment/decrement
        else if (e.Key == Key.Up && Value < Maximum)
        {
            Value++;
            e.Handled = true;
        }
        else if (e.Key == Key.Down && Value > Minimum)
        {
            Value--;
            e.Handled = true;
        }
        // Only allow numeric input
        else if (!IsNumericKey(e.Key))
        {
            e.Handled = true;
        }
    }

    private bool IsNumericKey(Key key)
    {
        return (key >= Key.D0 && key <= Key.D9) ||
               (key >= Key.NumPad0 && key <= Key.NumPad9) ||
               key == Key.Back || key == Key.Delete ||
               key == Key.Left || key == Key.Right ||
               key == Key.Tab || key == Key.Enter;
    }

    private void IncrementButton_Click(object sender, RoutedEventArgs e)
    {
        if (Value < Maximum)
        {
            Value++;
        }
    }

    private void DecrementButton_Click(object sender, RoutedEventArgs e)
    {
        if (Value > Minimum)
        {
            Value--;
        }
    }

    private void UpdateButtonStates()
    {
        if (DecrementButton != null)
            DecrementButton.IsEnabled = Value > Minimum;

        if (IncrementButton != null)
            IncrementButton.IsEnabled = Value < Maximum;
    }
}
