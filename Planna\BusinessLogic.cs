using System.ComponentModel;

namespace Planna;

public class BusinessLogic : INotifyPropertyChanged
{
    // Pricing Tiers
    private decimal _basicPlan = 50;
    private decimal _standardPlan = 99;
    private decimal _professionalPlan = 199;
    private decimal _enterprisePlan = 400;

    // Customer Mix (percentages)
    private decimal _basicMixPercent = 49;
    private decimal _standardMixPercent = 45;
    private decimal _professionalMixPercent = 5;
    private decimal _enterpriseMixPercent = 1;

    // Team Productivity
    private decimal _customersPerSalesRep = 6.5m;
    private int _salesRampMonths = 2;
    private int _leadsPerMarketer = 200;
    private decimal _leadConversionPercent = 12;

    // Salaries
    private decimal _salesRepSalary = 3000;
    private decimal _marketingSalary = 3000;
    private decimal _developerSalary = 4000;
    private decimal _operationsSalary = 3000;
    private decimal _leadershipSalary = 5000;
    private decimal _fixedMonthlyCosts = 2000;

    // Financial Settings
    private decimal _freeToPayConversionPercent = 10;
    private decimal _monthlyChurnPercent = 2;
    private decimal _initialCash = 151500;

    // Event Lead Generation
    private decimal _eventLeadGenerationRate = 25m; // Base rate: 25 free users per $1000
    private decimal _mediumEventScalingFactor = 0.9m; // 10% less effective
    private decimal _largeEventScalingFactor = 0.8m; // 20% less effective
    private decimal _mediumEventThreshold = 5000m;
    private decimal _largeEventThreshold = 10000m;

    public BusinessLogic()
    {
        // Initialize calculated properties
        CalculateARPU();
        ValidateCustomerMix();
    }

    #region Properties

    public decimal BasicPlan
    {
        get => _basicPlan;
        set { _basicPlan = value; OnPropertyChanged(); CalculateARPU(); }
    }

    public decimal StandardPlan
    {
        get => _standardPlan;
        set { _standardPlan = value; OnPropertyChanged(); CalculateARPU(); }
    }

    public decimal ProfessionalPlan
    {
        get => _professionalPlan;
        set { _professionalPlan = value; OnPropertyChanged(); CalculateARPU(); }
    }

    public decimal EnterprisePlan
    {
        get => _enterprisePlan;
        set { _enterprisePlan = value; OnPropertyChanged(); CalculateARPU(); }
    }

    public decimal BasicMixPercent
    {
        get => _basicMixPercent;
        set { _basicMixPercent = value; OnPropertyChanged(); CalculateARPU(); ValidateCustomerMix(); }
    }

    public decimal StandardMixPercent
    {
        get => _standardMixPercent;
        set { _standardMixPercent = value; OnPropertyChanged(); CalculateARPU(); ValidateCustomerMix(); }
    }

    public decimal ProfessionalMixPercent
    {
        get => _professionalMixPercent;
        set { _professionalMixPercent = value; OnPropertyChanged(); CalculateARPU(); ValidateCustomerMix(); }
    }

    public decimal EnterpriseMixPercent
    {
        get => _enterpriseMixPercent;
        set { _enterpriseMixPercent = value; OnPropertyChanged(); CalculateARPU(); ValidateCustomerMix(); }
    }

    public decimal CustomersPerSalesRep
    {
        get => _customersPerSalesRep;
        set { _customersPerSalesRep = value; OnPropertyChanged(); }
    }

    public int SalesRampMonths
    {
        get => _salesRampMonths;
        set { _salesRampMonths = value; OnPropertyChanged(); }
    }

    public int LeadsPerMarketer
    {
        get => _leadsPerMarketer;
        set { _leadsPerMarketer = value; OnPropertyChanged(); }
    }

    public decimal LeadConversionPercent
    {
        get => _leadConversionPercent;
        set { _leadConversionPercent = value; OnPropertyChanged(); }
    }

    public decimal SalesRepSalary
    {
        get => _salesRepSalary;
        set { _salesRepSalary = value; OnPropertyChanged(); }
    }

    public decimal MarketingSalary
    {
        get => _marketingSalary;
        set { _marketingSalary = value; OnPropertyChanged(); }
    }

    public decimal DeveloperSalary
    {
        get => _developerSalary;
        set { _developerSalary = value; OnPropertyChanged(); }
    }

    public decimal OperationsSalary
    {
        get => _operationsSalary;
        set { _operationsSalary = value; OnPropertyChanged(); }
    }

    public decimal LeadershipSalary
    {
        get => _leadershipSalary;
        set { _leadershipSalary = value; OnPropertyChanged(); }
    }

    public decimal FixedMonthlyCosts
    {
        get => _fixedMonthlyCosts;
        set { _fixedMonthlyCosts = value; OnPropertyChanged(); }
    }

    public decimal FreeToPayConversionPercent
    {
        get => _freeToPayConversionPercent;
        set { _freeToPayConversionPercent = value; OnPropertyChanged(); }
    }

    public decimal MonthlyChurnPercent
    {
        get => _monthlyChurnPercent;
        set { _monthlyChurnPercent = value; OnPropertyChanged(); }
    }

    public decimal InitialCash
    {
        get => _initialCash;
        set { _initialCash = value; OnPropertyChanged(); }
    }

    public decimal EventLeadGenerationRate
    {
        get => _eventLeadGenerationRate;
        set { _eventLeadGenerationRate = value; OnPropertyChanged(); }
    }

    public decimal MediumEventScalingFactor
    {
        get => _mediumEventScalingFactor;
        set { _mediumEventScalingFactor = value; OnPropertyChanged(); }
    }

    public decimal LargeEventScalingFactor
    {
        get => _largeEventScalingFactor;
        set { _largeEventScalingFactor = value; OnPropertyChanged(); }
    }

    public decimal MediumEventThreshold
    {
        get => _mediumEventThreshold;
        set { _mediumEventThreshold = value; OnPropertyChanged(); }
    }

    public decimal LargeEventThreshold
    {
        get => _largeEventThreshold;
        set { _largeEventThreshold = value; OnPropertyChanged(); }
    }

    #endregion

    #region Calculated Properties

    public decimal BlendedARPU { get; private set; }
    public decimal TotalCustomerMixPercent { get; private set; }
    public bool IsCustomerMixValid { get; private set; }
    public string CustomerMixValidationMessage { get; private set; } = "";

    #endregion

    #region Calculations

    private void CalculateARPU()
    {
        BlendedARPU = (BasicPlan * BasicMixPercent / 100) +
                      (StandardPlan * StandardMixPercent / 100) +
                      (ProfessionalPlan * ProfessionalMixPercent / 100) +
                      (EnterprisePlan * EnterpriseMixPercent / 100);

        OnPropertyChanged(nameof(BlendedARPU));
    }

    private void ValidateCustomerMix()
    {
        TotalCustomerMixPercent = BasicMixPercent + StandardMixPercent + ProfessionalMixPercent + EnterpriseMixPercent;
        IsCustomerMixValid = Math.Abs(TotalCustomerMixPercent - 100) < 0.01m;

        CustomerMixValidationMessage = TotalCustomerMixPercent switch
        {
            < 100 => $"Total: {TotalCustomerMixPercent:F1}% (Need {100 - TotalCustomerMixPercent:F1}% more)",
            > 100 => $"Total: {TotalCustomerMixPercent:F1}% (Reduce by {TotalCustomerMixPercent - 100:F1}%)",
            _ => $"Total: {TotalCustomerMixPercent:F1}% ✓"
        };

        OnPropertyChanged(nameof(TotalCustomerMixPercent));
        OnPropertyChanged(nameof(IsCustomerMixValid));
        OnPropertyChanged(nameof(CustomerMixValidationMessage));
    }

    public decimal CalculateMonthlyBurn(MonthlyPlanData monthData)
    {
        var teamCost = (monthData.SalesReps * SalesRepSalary) +
                       (monthData.MarketingTeam * MarketingSalary) +
                       (monthData.DevTeam * DeveloperSalary) +
                       (monthData.OpsTeam * OperationsSalary) +
                       (monthData.LeadershipTeam * LeadershipSalary);

        return teamCost + monthData.EventsCost + FixedMonthlyCosts;
    }

    public decimal CalculateMRR(int totalCustomers)
    {
        return totalCustomers * BlendedARPU;
    }

    public decimal CalculateCustomerAcquisitionCost(decimal salesCost, decimal marketingCost, decimal eventsCost, int newCustomers)
    {
        if (newCustomers == 0) return 0;
        return (salesCost + marketingCost + eventsCost) / newCustomers;
    }

    public decimal CalculateLifetimeValue(decimal avgCustomerLifeMonths)
    {
        return BlendedARPU * avgCustomerLifeMonths;
    }

    public decimal CalculateRunwayMonths(decimal currentCash, decimal monthlyBurn)
    {
        if (monthlyBurn <= 0) return decimal.MaxValue;
        return currentCash / monthlyBurn;
    }

    public decimal CalculateLTVToCACRatio(decimal ltv, decimal cac)
    {
        if (cac <= 0) return 0;
        return ltv / cac;
    }

    public decimal CalculatePaybackPeriodMonths(decimal cac, decimal monthlyRevenue)
    {
        if (monthlyRevenue <= 0) return decimal.MaxValue;
        return cac / monthlyRevenue;
    }

    public decimal CalculateGrossMargin(decimal revenue, decimal cogs)
    {
        if (revenue <= 0) return 0;
        return ((revenue - cogs) / revenue) * 100;
    }

    public decimal CalculateMonthlyGrowthRate(decimal currentMRR, decimal previousMRR)
    {
        if (previousMRR <= 0) return 0;
        return ((currentMRR - previousMRR) / previousMRR) * 100;
    }

    public decimal CalculateNetRevenueRetention(decimal startingMRR, decimal expansionMRR, decimal churnMRR)
    {
        if (startingMRR <= 0) return 0;
        return ((startingMRR + expansionMRR - churnMRR) / startingMRR) * 100;
    }

    #endregion

    #region Industry Benchmarks & Health Indicators

    public static class IndustryBenchmarks
    {
        // LTV:CAC Ratio benchmarks
        public const decimal LTVCACExcellent = 5.0m;
        public const decimal LTVCACGood = 3.0m;
        public const decimal LTVCACAcceptable = 2.0m;

        // Payback Period (months)
        public const decimal PaybackExcellent = 12m;
        public const decimal PaybackGood = 18m;
        public const decimal PaybackAcceptable = 24m;

        // Monthly Churn Rate (%)
        public const decimal ChurnExcellent = 2m;
        public const decimal ChurnGood = 5m;
        public const decimal ChurnAcceptable = 10m;

        // Gross Margin (%)
        public const decimal GrossMarginExcellent = 80m;
        public const decimal GrossMarginGood = 70m;
        public const decimal GrossMarginAcceptable = 60m;

        // Monthly Growth Rate (%)
        public const decimal GrowthRateExcellent = 20m;
        public const decimal GrowthRateGood = 15m;
        public const decimal GrowthRateAcceptable = 10m;

        // Net Revenue Retention (%)
        public const decimal NRRExcellent = 120m;
        public const decimal NRRGood = 110m;
        public const decimal NRRAcceptable = 100m;

        // Runway (months)
        public const decimal RunwayExcellent = 18m;
        public const decimal RunwayGood = 12m;
        public const decimal RunwayAcceptable = 6m;
    }

    public enum HealthStatus
    {
        Excellent,
        Good,
        Acceptable,
        Poor,
        Critical
    }

    public static HealthStatus GetLTVCACHealth(decimal ratio)
    {
        return ratio switch
        {
            >= IndustryBenchmarks.LTVCACExcellent => HealthStatus.Excellent,
            >= IndustryBenchmarks.LTVCACGood => HealthStatus.Good,
            >= IndustryBenchmarks.LTVCACAcceptable => HealthStatus.Acceptable,
            > 0 => HealthStatus.Poor,
            _ => HealthStatus.Critical
        };
    }

    public static HealthStatus GetPaybackPeriodHealth(decimal months)
    {
        return months switch
        {
            <= IndustryBenchmarks.PaybackExcellent => HealthStatus.Excellent,
            <= IndustryBenchmarks.PaybackGood => HealthStatus.Good,
            <= IndustryBenchmarks.PaybackAcceptable => HealthStatus.Acceptable,
            < 36 => HealthStatus.Poor,
            _ => HealthStatus.Critical
        };
    }

    public static HealthStatus GetChurnHealth(decimal churnPercent)
    {
        return churnPercent switch
        {
            <= IndustryBenchmarks.ChurnExcellent => HealthStatus.Excellent,
            <= IndustryBenchmarks.ChurnGood => HealthStatus.Good,
            <= IndustryBenchmarks.ChurnAcceptable => HealthStatus.Acceptable,
            <= 15 => HealthStatus.Poor,
            _ => HealthStatus.Critical
        };
    }

    public static HealthStatus GetGrowthRateHealth(decimal growthPercent)
    {
        return growthPercent switch
        {
            >= IndustryBenchmarks.GrowthRateExcellent => HealthStatus.Excellent,
            >= IndustryBenchmarks.GrowthRateGood => HealthStatus.Good,
            >= IndustryBenchmarks.GrowthRateAcceptable => HealthStatus.Acceptable,
            >= 5 => HealthStatus.Poor,
            _ => HealthStatus.Critical
        };
    }

    public static HealthStatus GetRunwayHealth(decimal months)
    {
        return months switch
        {
            >= IndustryBenchmarks.RunwayExcellent => HealthStatus.Excellent,
            >= IndustryBenchmarks.RunwayGood => HealthStatus.Good,
            >= IndustryBenchmarks.RunwayAcceptable => HealthStatus.Acceptable,
            >= 3 => HealthStatus.Poor,
            _ => HealthStatus.Critical
        };
    }

    public static string GetHealthColor(HealthStatus status)
    {
        return status switch
        {
            HealthStatus.Excellent => "#22C55E", // Green
            HealthStatus.Good => "#84CC16", // Light Green
            HealthStatus.Acceptable => "#EAB308", // Yellow
            HealthStatus.Poor => "#F97316", // Orange
            HealthStatus.Critical => "#EF4444", // Red
            _ => "#6B7280" // Gray
        };
    }

    public static string GetHealthMessage(HealthStatus status, string metricName)
    {
        return status switch
        {
            HealthStatus.Excellent => $"🎉 Excellent {metricName} - Top 10% of SaaS companies",
            HealthStatus.Good => $"✅ Good {metricName} - Above industry average",
            HealthStatus.Acceptable => $"⚠️ Acceptable {metricName} - Industry average",
            HealthStatus.Poor => $"🔶 Poor {metricName} - Below industry average",
            HealthStatus.Critical => $"🚨 Critical {metricName} - Immediate attention needed",
            _ => $"❓ Unknown {metricName}"
        };
    }

    #endregion

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}


