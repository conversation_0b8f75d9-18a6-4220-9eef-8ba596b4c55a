<UserControl x:Class="Planna.TeamSizeEditor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Planna"
             mc:Ignorable="d"
             d:DesignHeight="32" d:DesignWidth="120">
    <UserControl.Resources>
        <!-- Modern button styles -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F3F2F1"/>
            <Setter Property="BorderBrush" Value="#E1DFDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="#323130"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E1DFDD"/>
                                <Setter Property="BorderBrush" Value="#C8C6C4"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#D2D0CE"/>
                                <Setter Property="BorderBrush" Value="#B3B0AD"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#F8F8F8"/>
                                <Setter Property="BorderBrush" Value="#EDEBE9"/>
                                <Setter Property="Foreground" Value="#A19F9D"/>
                                <Setter Property="Cursor" Value="Arrow"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="DecrementButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FFF4CE"/>
                    <Setter Property="BorderBrush" Value="#FFE69C"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#FFE69C"/>
                    <Setter Property="BorderBrush" Value="#FFDB6B"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="IncrementButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#DDF4FF"/>
                    <Setter Property="BorderBrush" Value="#92C5F7"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#92C5F7"/>
                    <Setter Property="BorderBrush" Value="#0078D4"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ValueTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E1DFDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#0078D4"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="#C8C6C4"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="28"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="28"/>
        </Grid.ColumnDefinitions>

        <Button Grid.Column="0" Content="−" Width="28" Height="28"
                FontSize="16" FontWeight="Bold"
                Click="DecrementButton_Click"
                Style="{StaticResource DecrementButtonStyle}"
                ToolTip="Decrease team size (Ctrl+Down)"
                x:Name="DecrementButton"/>

        <TextBox Grid.Column="1" x:Name="ValueTextBox"
                 Margin="2,0"
                 Style="{StaticResource ValueTextBoxStyle}"
                 ToolTip="Enter team size directly or use +/- buttons"/>

        <Button Grid.Column="2" Content="+" Width="28" Height="28"
                FontSize="16" FontWeight="Bold"
                Click="IncrementButton_Click"
                Style="{StaticResource IncrementButtonStyle}"
                ToolTip="Increase team size (Ctrl+Up)"
                x:Name="IncrementButton"/>
    </Grid>
</UserControl>
