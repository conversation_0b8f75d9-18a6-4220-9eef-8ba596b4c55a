# Royaltea Growth Simulator - Simplified Product Requirements Document

## Overview
A streamlined month-by-month growth planning tool that allows real-time visualization of how team scaling decisions impact key business metrics.

## Core Components

### 1. **Benchmark Variables Panel** (Top Section)
Configuration area for key assumptions that apply across all months:

```
REVENUE ASSUMPTIONS
├── Pricing Tiers
│   ├── Basic: $50/month
│   ├── Standard: $99/month  
│   ├── Professional: $199/month
│   └── Enterprise: $400/month
│
├── Customer Mix
│   ├── Basic: 49%
│   ├── Standard: 45%
│   ├── Professional: 5%
│   └── Enterprise: 1%
│   └── Blended ARPU: $83 (auto-calculated)
│
└── Conversion Metrics
    ├── Free-to-paid conversion: 10%
    └── Monthly churn rate: 2%

EMPLOYEE PRODUCTIVITY
├── Sales Performance
│   ├── Customers per rep per month: 6.5
│   ├── Ramp time: 2 months
│   └── Ramp efficiency: 50% month 1, 75% month 2, 100% month 3+
│
└── Marketing Performance
    ├── Leads per marketer per month: 200
    └── Lead-to-customer conversion: 12%

SALARY BENCHMARKS (per month)
├── Sales Rep: $3,000
├── Marketing: $3,000
├── Developer: $4,000
├── Operations: $3,000
└── Leadership: $5,000

STARTING CONDITIONS
├── Initial cash: $151,500
├── Starting MRR: $0
├── Starting customers: 0
└── Monthly fixed costs: $2,000
```

### 2. **Interactive Chart Area** (Middle Section)

**Chart Features:**
- Multi-line chart with toggleable metrics
- X-axis: Months (Aug 2025 - Jul 2030)
- Y-axis: Dynamic based on selected metric
- Hover tooltip showing exact values

**Filterable Metrics:**
- Monthly Recurring Revenue (MRR)
- Total Customers
- New Customers This Month
- Cash on Hand
- Monthly Burn Rate
- Runway (months remaining)
- Team Size
- CAC (Customer Acquisition Cost)
- LTV:CAC Ratio

**Visual Design:**
```
┌─────────────────────────────────────────────────────────────┐
│  Metrics: [✓ MRR] [✓ Customers] [  Cash] [  Burn] [  Team] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  $100K ┤                                          ╱        │
│        │                                      ╱╱╱          │
│   $75K ┤                                  ╱╱╱              │
│        │                              ╱╱╱                  │
│   $50K ┤                         ╱╱╱╱                      │
│        │                    ╱╱╱╱ [Hover: Feb 2026]        │
│   $25K ┤              ╱╱╱╱╱   MRR: $48,061               │
│        │         ╱╱╱╱╱       Customers: 578              │
│     $0 └─────╱╱╱────────────────────────────────────────  │
│        Aug '25   Nov '25   Feb '26   May '26   Aug '26     │
└─────────────────────────────────────────────────────────────┘
```

### 3. **Unit Economics Display** (Right Panel)

Real-time calculations based on current month selection:

```
UNIT ECONOMICS (Feb 2026)
┌─────────────────────────┐
│ Customer Metrics        │
├─────────────────────────┤
│ Total Customers: 578    │
│ New This Month: 164     │
│ Blended ARPU: $83       │
│ MRR: $48,061           │
│ ARR: $576,732          │
├─────────────────────────┤
│ Acquisition Costs       │
├─────────────────────────┤
│ Sales Team Cost: $15K   │
│ Marketing Cost: $12K    │
│ Events This Month: $5K  │
│ Total CAC: $195         │
├─────────────────────────┤
│ Lifetime Value          │
├─────────────────────────┤
│ Avg Customer Life: 30mo │
│ LTV: $2,490            │
│ LTV:CAC Ratio: 12.8:1  │
│ Payback Period: 2.3mo   │
├─────────────────────────┤
│ Financial Health        │
├─────────────────────────┤
│ Cash on Hand: $89,425   │
│ Monthly Burn: $32,000   │
│ Runway: 2.8 months      │
│ Break-even: May 2026    │
└─────────────────────────┘
```

### 4. **Month-by-Month Input Table** (Bottom Section)

Scrollable table for entering team composition and costs:

```
┌─────────┬────────┬────────┬────────┬────────┬────────┬────────┬────────┐
│  Month  │ Sales  │ Mktg   │ Dev    │ Ops    │ Leader │ Events │ Total  │
│         │ Reps   │ Team   │ Team   │ Team   │ Team   │ Cost   │ Burn   │
├─────────┼────────┼────────┼────────┼────────┼────────┼────────┼────────┤
│ Aug '25 │   1    │   0    │   0    │   0    │   0    │ $2,000 │ $5,000 │
│ Sep '25 │   2    │   1    │   0    │   0    │   0    │ $3,500 │ $11,500│
│ Oct '25 │   3    │   1    │   0    │   0    │   0    │ $2,500 │ $14,500│
│ Nov '25 │   4    │   2    │   0    │   0    │   0    │ $5,000 │ $23,000│
│ Dec '25 │   5    │   3    │   0    │   0    │   0    │ $8,000 │ $32,000│
│ Jan '26 │   5    │   4    │   0    │   1    │   0    │ $10,000│ $40,000│
│ Feb '26 │   5    │   4    │   0    │   1    │   0    │ $5,000 │ $35,000│
└─────────┴────────┴────────┴────────┴────────┴────────┴────────┴────────┘
```

**Table Features:**
- Click any cell to edit
- Tab/Enter to move between cells
- Auto-calculation of total burn
- Visual indicators when cash runs low
- Copy/paste support for Excel import

## Key Calculations

### Customer Growth Formula
```javascript
function calculateNewCustomers(month) {
  const salesReps = month.salesTeam;
  const marketers = month.marketingTeam;
  const monthsSinceHire = calculateMonthsSinceHire(salesReps);
  
  // Sales contribution with ramp consideration
  const salesContribution = salesReps.reduce((total, rep) => {
    const efficiency = getRampEfficiency(rep.monthsActive);
    return total + (CUSTOMERS_PER_REP * efficiency);
  }, 0);
  
  // Marketing contribution
  const leads = marketers * LEADS_PER_MARKETER;
  const marketingContribution = leads * LEAD_CONVERSION_RATE;
  
  // Event contribution
  const eventContribution = (month.eventCost / 1000) * EVENT_ROI_MULTIPLIER;
  
  return Math.round(salesContribution + marketingContribution + eventContribution);
}
```

### Financial Calculations
```javascript
function calculateFinancials(month, previousMonth) {
  const newCustomers = calculateNewCustomers(month);
  const totalCustomers = previousMonth.totalCustomers + newCustomers - churnedCustomers;
  const MRR = totalCustomers * BLENDED_ARPU;
  
  const teamCost = 
    (month.salesTeam * SALES_SALARY) +
    (month.marketingTeam * MARKETING_SALARY) +
    (month.devTeam * DEV_SALARY) +
    (month.opsTeam * OPS_SALARY) +
    (month.leadershipTeam * LEADERSHIP_SALARY);
    
  const totalBurn = teamCost + month.eventCost + FIXED_COSTS;
  const netCashFlow = MRR - totalBurn;
  const cashOnHand = previousMonth.cashOnHand + netCashFlow;
  
  return {
    newCustomers,
    totalCustomers,
    MRR,
    totalBurn,
    netCashFlow,
    cashOnHand,
    runway: cashOnHand / totalBurn
  };
}
```

## User Interface Behavior

### Interactive Elements
1. **Benchmark Variables**: 
   - Slider inputs for percentages
   - Number inputs for dollar amounts
   - Changes immediately recalculate all months

2. **Chart Interactions**:
   - Click legend items to toggle metrics
   - Hover for detailed tooltips
   - Click and drag to zoom
   - Double-click to reset zoom

3. **Table Editing**:
   - Single click to select cell
   - Double click to edit
   - Arrow keys for navigation
   - Ctrl+C/V for copy/paste

### Visual Feedback
- **Green highlights**: Positive cash flow months
- **Yellow warnings**: Low runway (<3 months)
- **Red alerts**: Negative cash position
- **Growth indicators**: Up/down arrows for MoM changes

## Export & Sharing

### Export Options
1. **Export CSV**: Native file save dialog for full data table with calculations
2. **Export Chart**: Save PNG/SVG images of current chart view to chosen location
3. **Export Scenario**: Save complete scenario as JSON file for sharing
4. **Print Report**: Generate and save formatted PDF reports with native print dialog
5. **Excel Export**: Export formatted spreadsheet with formulas intact

### Save/Load Scenarios
- **Local Storage**: SQLite database for fast scenario management
- **File-based Sharing**: Export/import scenario files (.planna format)
- **Multiple Scenarios**: Compare up to 3 scenarios side-by-side
- **Auto-save**: Continuous saving every 30 seconds to prevent data loss
- **Backup/Restore**: Full database backup and restore functionality

## Technical Implementation

### Tech Stack (Desktop Application)
- **Desktop Framework**: Tauri (Rust backend) or Electron for cross-platform desktop app
- **Frontend**: React + TypeScript with Vite for fast development
- **UI Components**: HeroUI (NextUI v2) for beautiful, accessible components
- **Charts**: Recharts for interactive visualizations
- **State Management**: Zustand for lightweight, performant state
- **Styling**: Tailwind CSS (integrated with HeroUI)
- **Build Tool**: Vite for fast builds and HMR
- **Data Persistence**:
  - SQLite database for robust local data storage
  - JSON files for scenario exports/imports
- **Animations**: Framer Motion (built into HeroUI)
- **Form Handling**: React Hook Form with Zod validation
- **Date Handling**: date-fns for month calculations
- **Export**:
  - Native file system access for saving files
  - jsPDF for PDF reports
  - CSV export with native save dialogs

### Performance Requirements
- **Instant recalculation** (<50ms) - Optimized with Zustand and React.memo
- **Smooth animations** - 60fps with Framer Motion and HeroUI transitions
- **Large datasets** - Support 60+ months with virtualized table rendering
- **Native performance** - Desktop-class performance with Tauri/Electron
- **Fast startup** - Sub-second app launch with Vite's optimized builds
- **Responsive UI** - Optimized for desktop screen sizes and interactions
- **Reliable data** - SQLite ensures data integrity and fast queries

## Success Metrics
- Replace existing spreadsheet within 1 week
- Generate investor-ready projections in <5 minutes  
- Model 10+ different scenarios per planning session
- Reduce financial planning time by 80%

This streamlined tool focuses specifically on Royaltea's needs: understanding how team scaling decisions impact growth, revenue, and runway on a month-by-month basis with clear visual feedback and easy experimentation.