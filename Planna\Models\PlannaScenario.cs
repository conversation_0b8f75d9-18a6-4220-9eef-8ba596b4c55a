using System.ComponentModel;
using Newtonsoft.Json;

namespace Planna.Models;

/// <summary>
/// Serializable representation of the complete application state
/// </summary>
public class PlannaScenario
{
    public string Name { get; set; } = "Untitled Scenario";
    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime LastModifiedDate { get; set; } = DateTime.Now;
    public string Version { get; set; } = "1.0";
    
    public BusinessLogicData BusinessLogic { get; set; } = new();
    public List<MonthlyPlanDataSerialized> MonthlyPlans { get; set; } = new();
}

/// <summary>
/// Serializable representation of BusinessLogic settings
/// </summary>
public class BusinessLogicData
{
    // Pricing Tiers
    public decimal BasicPlan { get; set; } = 50;
    public decimal StandardPlan { get; set; } = 99;
    public decimal ProfessionalPlan { get; set; } = 199;
    public decimal EnterprisePlan { get; set; } = 400;

    // Customer Mix (percentages)
    public decimal BasicMixPercent { get; set; } = 49;
    public decimal StandardMixPercent { get; set; } = 45;
    public decimal ProfessionalMixPercent { get; set; } = 5;
    public decimal EnterpriseMixPercent { get; set; } = 1;
    
    // Team Productivity
    public decimal CustomersPerSalesRep { get; set; } = 6.5m;
    public int SalesRampMonths { get; set; } = 2;
    public int LeadsPerMarketer { get; set; } = 200;
    public decimal LeadConversionPercent { get; set; } = 12;
    
    // Salaries
    public decimal SalesRepSalary { get; set; } = 3000;
    public decimal MarketingSalary { get; set; } = 3000;
    public decimal DeveloperSalary { get; set; } = 4000;
    public decimal OperationsSalary { get; set; } = 3000;
    public decimal LeadershipSalary { get; set; } = 5000;
    public decimal FixedMonthlyCosts { get; set; } = 2000;
    
    // Financial Settings
    public decimal FreeToPayConversionPercent { get; set; } = 10;
    public decimal MonthlyChurnPercent { get; set; } = 2;
    public decimal InitialCash { get; set; } = 151500;

    // Event Lead Generation
    public decimal EventLeadGenerationRate { get; set; } = 25m;
    public decimal MediumEventScalingFactor { get; set; } = 0.9m;
    public decimal LargeEventScalingFactor { get; set; } = 0.8m;
    public decimal MediumEventThreshold { get; set; } = 5000m;
    public decimal LargeEventThreshold { get; set; } = 10000m;

    // Calculated properties (will be recalculated on load)
    public decimal BlendedARPU { get; set; }
    public bool IsCustomerMixValid { get; set; }
}

/// <summary>
/// Serializable representation of MonthlyPlanData
/// </summary>
public class MonthlyPlanDataSerialized
{
    public string Month { get; set; } = string.Empty;
    public int SalesReps { get; set; }
    public int MarketingTeam { get; set; }
    public int DevTeam { get; set; }
    public int OpsTeam { get; set; }
    public int LeadershipTeam { get; set; }
    public decimal EventsCost { get; set; }
}
